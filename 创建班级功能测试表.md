# 创建班级功能测试表

## 模块信息
- **模块编号**: KSK-BJ-001
- **模块名称**: 创建班级
- **功能名称**: 教师创建虚拟班级
- **测试方法**: 等价类划分、边界值分析、决策表、因果图

## 功能描述
老师可以创建班级，添加班级成员。班级创建者可以解散班级、管理或删除成员，可以设置或取消成员的管理员身份。支持虚拟化的班级管理体系。

## 等价类划分

### 班级名称等价类
- **有效等价类**:
  - E1: 有效的班级名称(1-50个字符，包含汉字、字母、数字)
  - E2: 唯一的班级名称
- **无效等价类**:
  - E3: 空班级名称
  - E4: 超长班级名称(>50个字符)
  - E5: 包含特殊字符的班级名称
  - E6: 已存在的班级名称

### 班级描述等价类
- **有效等价类**:
  - E7: 有效的班级描述(0-200个字符)
- **无效等价类**:
  - E8: 超长班级描述(>200个字符)

### 班级类型等价类
- **有效等价类**:
  - E9: 公开班级
  - E10: 私有班级
- **无效等价类**:
  - E11: 未选择班级类型

### 班级容量等价类
- **有效等价类**:
  - E12: 合理的班级容量(1-500人)
- **无效等价类**:
  - E13: 班级容量为0
  - E14: 班级容量超限(>500人)

### 班级分类等价类
- **有效等价类**:
  - E15: 学历教育类
  - E16: 职业培训类
  - E17: 企业内训类
  - E18: 兴趣爱好类
- **无效等价类**:
  - E19: 未选择班级分类

## 测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 正常创建公开班级 | 输入班级名称"高一数学班"、描述"高一年级数学课程"、选择公开类型、设置容量50人，点击"创建" | 班级创建成功，显示在班级列表中 | 待测试 | 等价类E1+E7+E9+E12 |
| 2 | 正常创建私有班级 | 输入班级名称"VIP辅导班"、描述"小班制辅导"、选择私有类型、设置容量10人，点击"创建" | 班级创建成功，仅创建者可见 | 待测试 | 等价类E1+E7+E10+E12 |
| 3 | 班级名称为空 | 班级名称留空，其他信息正确填写，点击创建 | 提示"请输入班级名称" | 待测试 | 等价类E3 |
| 4 | 班级名称过长 | 输入51个字符的班级名称，其他信息正确 | 提示"班级名称过长"或自动截断 | 待测试 | 等价类E4 |
| 5 | 班级名称包含特殊字符 | 输入"数学班@#$%"，其他信息正确 | 提示"班级名称包含非法字符"或创建成功 | 待测试 | 等价类E5 |
| 6 | 班级名称已存在 | 输入已存在的班级名称，其他信息正确 | 提示"班级名称已存在" | 待测试 | 等价类E6 |
| 7 | 班级描述过长 | 输入201个字符的描述，其他信息正确 | 提示"描述过长"或自动截断 | 待测试 | 等价类E8 |
| 8 | 未选择班级类型 | 不选择班级类型，其他信息正确 | 提示"请选择班级类型"或使用默认类型 | 待测试 | 等价类E11 |
| 9 | 班级容量为0 | 设置班级容量为0，其他信息正确 | 提示"班级容量不能为0" | 待测试 | 等价类E13 |
| 10 | 班级容量超限 | 设置班级容量为501，其他信息正确 | 提示"班级容量超出限制"或自动调整 | 待测试 | 等价类E14 |
| 11 | 班级名称边界值-最短 | 输入1个字符"A"，其他信息正确 | 班级创建成功 | 待测试 | 边界值分析 |
| 12 | 班级名称边界值-最长 | 输入50个字符的班级名称，其他信息正确 | 班级创建成功 | 待测试 | 边界值分析 |
| 13 | 班级容量边界值-最小 | 设置班级容量为1，其他信息正确 | 班级创建成功 | 待测试 | 边界值分析 |
| 14 | 班级容量边界值-最大 | 设置班级容量为500，其他信息正确 | 班级创建成功 | 待测试 | 边界值分析 |
| 15 | 空描述创建班级 | 班级描述留空，其他信息正确 | 班级创建成功 | 待测试 | 边界值分析 |
| 16 | 创建学历教育类班级 | 选择"学历教育类"分类，其他信息正确 | 班级创建成功，归入学历教育类 | 待测试 | 等价类E15 |
| 17 | 创建职业培训类班级 | 选择"职业培训类"分类，其他信息正确 | 班级创建成功，归入职业培训类 | 待测试 | 等价类E16 |
| 18 | 创建企业内训类班级 | 选择"企业内训类"分类，其他信息正确 | 班级创建成功，归入企业内训类 | 待测试 | 等价类E17 |
| 19 | 重复创建同名班级 | 创建成功后再次创建同名班级 | 提示"班级名称已存在"或自动添加后缀 | 待测试 | 重复操作测试 |
| 20 | 创建班级权限验证 | 学生账号尝试创建班级 | 提示"权限不足"或功能不可见 | 待测试 | 权限测试 |

## 班级设置高级功能测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 21 | 设置班级头像 | 为班级上传头像图片 | 头像上传成功，正确显示 | 待测试 | 图片上传测试 |
| 22 | 设置班级公告 | 为班级添加公告信息 | 公告设置成功，成员可见 | 待测试 | 公告功能测试 |
| 23 | 设置加入审核 | 设置班级需要审核才能加入 | 审核设置生效 | 待测试 | 审核设置测试 |
| 24 | 设置班级标签 | 为班级添加标签便于分类 | 标签添加成功 | 待测试 | 标签功能测试 |
| 25 | 设置班级时间 | 设置班级的开始和结束时间 | 时间设置成功 | 待测试 | 时间设置测试 |

## 班级管理功能测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 26 | 查看班级信息 | 进入已创建的班级查看详细信息 | 显示完整的班级信息 | 待测试 | 信息查看测试 |
| 27 | 修改班级信息 | 修改班级名称、描述等信息 | 班级信息更新成功 | 待测试 | 编辑功能测试 |
| 28 | 邀请成员加入 | 通过邀请码或邮件邀请成员 | 邀请发送成功 | 待测试 | 邀请功能测试 |
| 29 | 审核加入申请 | 处理学生的加入申请 | 可以同意或拒绝申请 | 待测试 | 审核流程测试 |
| 30 | 设置班级管理员 | 将班级成员设置为管理员 | 成员获得管理员权限 | 待测试 | 权限管理测试 |
| 31 | 移除班级成员 | 选择班级成员进行移除操作 | 成员被移除，收到通知 | 待测试 | 成员管理测试 |
| 32 | 解散班级 | 解散已创建的班级 | 班级被删除，成员收到通知 | 待测试 | 删除功能测试 |
| 33 | 班级公开性切换 | 将私有班级改为公开或相反 | 班级类型切换成功 | 待测试 | 状态切换测试 |
| 34 | 导出班级成员 | 导出班级成员列表 | 成员信息导出成功 | 待测试 | 导出功能测试 |
| 35 | 班级统计信息 | 查看班级的统计数据 | 显示准确的统计信息 | 待测试 | 统计功能测试 |

## 决策表分析

### 创建班级决策表
| 条件 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 班级名称有效 | Y | Y | Y | Y | N | N | N | N |
| 用户是教师 | Y | Y | N | N | Y | Y | N | N |
| 名称未重复 | Y | N | Y | N | Y | N | Y | N |

| 动作 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 创建成功 | X | | | | | | | |
| 提示名称重复 | | X | | X | | X | | X |
| 提示权限不足 | | | X | X | | | X | X |
| 提示名称无效 | | | | | X | X | X | X |

## 因果图分析

### 输入条件
- C1: 班级名称有效
- C2: 用户是教师
- C3: 班级类型已选择
- C4: 班级容量合理

### 输出结果
- E1: 班级创建成功
- E2: 提示名称错误
- E3: 提示权限不足
- E4: 提示类型未选择
- E5: 提示容量错误

### 逻辑关系
- 创建成功: C1 AND C2 AND C3 AND C4 → E1
- 名称错误: NOT C1 → E2
- 权限不足: NOT C2 → E3
- 类型未选择: NOT C3 → E4
- 容量错误: NOT C4 → E5

## 测试数据准备
1. **班级名称**: 准备各种长度和格式的班级名称
2. **用户账号**: 准备教师和学生测试账号
3. **班级类型**: 准备公开和私有班级测试数据
4. **容量设置**: 准备不同的班级容量数值
5. **权限测试**: 准备不同权限级别的测试账号

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari
- 网络: 稳定的互联网连接
- 账号: 有效的教师测试账号
- 权限: 班级创建权限
- 存储: 足够的班级数据存储空间
