# 参加班级考试功能测试表

## 模块信息
- **模块编号**: KSK-KS-001
- **模块名称**: 参加班级考试
- **功能名称**: 学生参加在线考试
- **测试方法**: 等价类划分、边界值分析、状态转换、时间测试

## 功能描述
学生可以参加老师组织的班级考试。支持在线答题、自动保存、时间控制、防作弊等功能。考试结束后可以查看成绩和答案解析。

## 等价类划分

### 考试状态等价类
- **有效等价类**:
  - E1: 未开始的考试
  - E2: 进行中的考试
  - E3: 可参加的考试(在时间范围内)
- **无效等价类**:
  - E4: 已结束的考试
  - E5: 未发布的考试
  - E6: 已参加过的考试

### 学生状态等价类
- **有效等价类**:
  - E7: 班级成员
  - E8: 有考试权限的学生
- **无效等价类**:
  - E9: 非班级成员
  - E10: 被禁止考试的学生

### 答题内容等价类
- **有效等价类**:
  - E11: 单选题答案(A/B/C/D)
  - E12: 多选题答案(多个选项组合)
  - E13: 判断题答案(正确/错误)
  - E14: 填空题答案(文本内容)
  - E15: 问答题答案(长文本)
- **无效等价类**:
  - E16: 空答案
  - E17: 格式错误的答案
  - E18: 超长答案

### 考试时间等价类
- **有效等价类**:
  - E19: 在规定时间内答题
- **无效等价类**:
  - E20: 超时答题
  - E21: 提前结束

## 测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 查看可参加的考试 | 学生登录后查看考试列表 | 显示所有可参加的班级考试 | 待测试 | 基本功能测试 |
| 2 | 正常进入考试 | 点击"开始考试"按钮进入考试 | 成功进入考试界面，显示试题 | 待测试 | 等价类E2+E7+E8 |
| 3 | 非班级成员参加考试 | 非班级成员尝试参加考试 | 提示"无权限参加此考试" | 待测试 | 等价类E9 |
| 4 | 参加已结束的考试 | 尝试参加已结束的考试 | 提示"考试已结束" | 待测试 | 等价类E4 |
| 5 | 重复参加考试 | 已参加过的考试再次参加 | 提示"已参加过此考试"或显示成绩 | 待测试 | 等价类E6 |
| 6 | 单选题答题 | 选择单选题的一个选项并提交 | 答案保存成功 | 待测试 | 等价类E11 |
| 7 | 多选题答题 | 选择多选题的多个选项并提交 | 答案保存成功 | 待测试 | 等价类E12 |
| 8 | 判断题答题 | 选择判断题的正确或错误并提交 | 答案保存成功 | 待测试 | 等价类E13 |
| 9 | 填空题答题 | 在填空题中输入答案并提交 | 答案保存成功 | 待测试 | 等价类E14 |
| 10 | 问答题答题 | 在问答题中输入长文本答案 | 答案保存成功 | 待测试 | 等价类E15 |
| 11 | 空答案提交 | 不选择任何选项直接提交 | 提示"请选择答案"或允许空答案 | 待测试 | 等价类E16 |
| 12 | 修改已答题目 | 修改已经作答的题目答案 | 答案修改成功 | 待测试 | 答案修改测试 |
| 13 | 跳题答题 | 跳过某些题目先答后面的题 | 可以跳题，答案正确保存 | 待测试 | 跳题功能测试 |
| 14 | 自动保存功能 | 答题过程中的自动保存 | 答案自动保存，刷新后仍存在 | 待测试 | 自动保存测试 |
| 15 | 手动保存功能 | 点击"保存"按钮手动保存 | 答案保存成功，显示保存提示 | 待测试 | 手动保存测试 |
| 16 | 考试时间显示 | 查看考试剩余时间 | 正确显示剩余时间，实时更新 | 待测试 | 时间显示测试 |
| 17 | 考试时间到自动提交 | 考试时间结束时的处理 | 自动提交试卷，显示提交成功 | 待测试 | 等价类E20 |
| 18 | 提前交卷 | 在规定时间内主动提交试卷 | 试卷提交成功，考试结束 | 待测试 | 等价类E21 |
| 19 | 查看答题进度 | 查看已答题目和未答题目 | 正确显示答题进度 | 待测试 | 进度显示测试 |
| 20 | 考试结果查看 | 考试结束后查看成绩 | 显示考试成绩和答题情况 | 待测试 | 结果查看测试 |

## 考试界面功能测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 21 | 题目导航功能 | 使用题目导航跳转到指定题目 | 成功跳转到指定题目 | 待测试 | 导航功能测试 |
| 22 | 答题卡功能 | 使用答题卡查看答题状态 | 正确显示已答和未答状态 | 待测试 | 答题卡测试 |
| 23 | 题目标记功能 | 标记需要重点检查的题目 | 题目标记成功，便于后续检查 | 待测试 | 标记功能测试 |
| 24 | 字体大小调整 | 调整考试界面的字体大小 | 字体大小调整成功 | 待测试 | 界面调整测试 |
| 25 | 全屏模式 | 切换到全屏考试模式 | 成功进入全屏模式 | 待测试 | 全屏功能测试 |

## 防作弊功能测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 26 | 切换窗口检测 | 考试中切换到其他窗口 | 系统记录切换行为或警告 | 待测试 | 防作弊测试 |
| 27 | 复制粘贴限制 | 尝试复制粘贴考试内容 | 复制粘贴功能被限制 | 待测试 | 防作弊测试 |
| 28 | 右键菜单禁用 | 在考试页面点击右键 | 右键菜单被禁用 | 待测试 | 防作弊测试 |
| 29 | 刷新页面处理 | 考试中刷新浏览器页面 | 答案保留，可继续考试 | 待测试 | 异常处理测试 |
| 30 | 网络断开处理 | 考试中网络连接中断 | 答案本地保存，网络恢复后同步 | 待测试 | 网络异常测试 |

## 时间控制测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 31 | 考试时间边界值-最后1分钟 | 在考试最后1分钟答题 | 正常答题，时间准确显示 | 待测试 | 边界值分析 |
| 32 | 考试时间边界值-最后10秒 | 在考试最后10秒提交 | 成功提交，记录准确时间 | 待测试 | 边界值分析 |
| 33 | 超时1秒提交 | 超时1秒后尝试提交 | 提交失败或自动提交 | 待测试 | 边界值分析 |
| 34 | 时间显示准确性 | 验证倒计时的准确性 | 倒计时与实际时间一致 | 待测试 | 时间准确性测试 |
| 35 | 暂停考试功能 | 如果支持暂停，测试暂停功能 | 考试暂停，时间停止计算 | 待测试 | 暂停功能测试 |

## 状态转换测试

### 考试状态转换图
```
未开始 → 进行中 → 已完成
       ↓        ↓
       已取消 ← 超时结束
```

| 编号 | 状态转换 | 测试条件 | 预期结果 | 测试方法 |
|------|---------|---------|---------|---------|
| 36 | 未开始→进行中 | 点击开始考试 | 状态变为进行中 | 状态转换测试 |
| 37 | 进行中→已完成 | 主动提交试卷 | 状态变为已完成 | 状态转换测试 |
| 38 | 进行中→超时结束 | 考试时间到 | 自动提交，状态变为超时结束 | 状态转换测试 |
| 39 | 任意状态→已取消 | 管理员取消考试 | 状态变为已取消 | 状态转换测试 |
| 40 | 状态查询 | 查询考试当前状态 | 返回正确的状态信息 | 状态查询测试 |

## 测试数据准备
1. **考试数据**: 准备不同类型和状态的考试
2. **学生账号**: 准备班级成员和非成员账号
3. **试题数据**: 准备各种类型的试题
4. **时间设置**: 准备不同时长的考试
5. **答案数据**: 准备各种格式的标准答案

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari (支持JavaScript)
- 网络: 稳定的互联网连接
- 账号: 有效的学生测试账号
- 权限: 考试参与权限
- 时间: 准确的系统时间
- 分辨率: 1024x768以上分辨率
