# 查看待办事宜功能测试表

## 模块信息
- **模块编号**: KSK-DB-001
- **模块名称**: 查看待办事宜
- **功能名称**: 待办事宜中心、事务提醒
- **测试方法**: 等价类划分、边界值分析、状态转换、功能测试

## 功能描述
待办事宜中心显示需要用户处理的各种事务，包括待审核的申请、待评阅的答卷、待处理的消息、即将到期的考试等。支持事务分类、优先级排序、状态管理等功能。

## 等价类划分

### 待办事宜类型等价类
- **有效等价类**:
  - E1: 待审核申请(学生加入班级申请)
  - E2: 待评阅答卷(需要手工评阅的试卷)
  - E3: 待处理消息(未读的重要消息)
  - E4: 即将到期考试(考试截止时间临近)
  - E5: 待发布成绩(已评阅但未发布的成绩)
  - E6: 系统通知(系统重要通知)
- **无效等价类**:
  - E7: 已处理事宜
  - E8: 已过期事宜

### 事宜状态等价类
- **有效等价类**:
  - E9: 未处理状态
  - E10: 处理中状态
  - E11: 紧急状态
- **无效等价类**:
  - E12: 已完成状态
  - E13: 已取消状态

### 优先级等价类
- **有效等价类**:
  - E14: 高优先级(紧急)
  - E15: 中优先级(重要)
  - E16: 低优先级(一般)
- **无效等价类**:
  - E17: 无优先级

### 时间范围等价类
- **有效等价类**:
  - E18: 今日待办
  - E19: 本周待办
  - E20: 本月待办
  - E21: 逾期待办
- **无效等价类**:
  - E22: 无效时间范围

## 测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 查看待办事宜列表 | 登录后进入待办事宜中心 | 显示所有未处理的待办事宜 | 待测试 | 基本功能测试 |
| 2 | 查看待审核申请 | 点击"待审核申请"分类 | 显示所有待审核的学生申请 | 待测试 | 等价类E1 |
| 3 | 查看待评阅答卷 | 点击"待评阅答卷"分类 | 显示所有需要评阅的答卷 | 待测试 | 等价类E2 |
| 4 | 查看待处理消息 | 点击"待处理消息"分类 | 显示所有未读的重要消息 | 待测试 | 等价类E3 |
| 5 | 查看即将到期考试 | 点击"即将到期考试"分类 | 显示即将截止的考试列表 | 待测试 | 等价类E4 |
| 6 | 查看待发布成绩 | 点击"待发布成绩"分类 | 显示已评阅但未发布的成绩 | 待测试 | 等价类E5 |
| 7 | 查看系统通知 | 点击"系统通知"分类 | 显示系统发出的重要通知 | 待测试 | 等价类E6 |
| 8 | 按优先级排序 | 选择按优先级排序待办事宜 | 高优先级事宜排在前面 | 待测试 | 等价类E14+E15+E16 |
| 9 | 按时间排序 | 选择按时间排序待办事宜 | 最新的事宜排在前面 | 待测试 | 时间排序测试 |
| 10 | 筛选今日待办 | 筛选显示今日的待办事宜 | 只显示今天需要处理的事宜 | 待测试 | 等价类E18 |
| 11 | 筛选本周待办 | 筛选显示本周的待办事宜 | 只显示本周需要处理的事宜 | 待测试 | 等价类E19 |
| 12 | 筛选逾期待办 | 筛选显示逾期的待办事宜 | 只显示已逾期的事宜 | 待测试 | 等价类E21 |
| 13 | 标记为已处理 | 将待办事宜标记为已处理 | 事宜状态变为已处理 | 待测试 | 状态管理测试 |
| 14 | 批量处理事宜 | 选择多个事宜进行批量处理 | 所选事宜都被处理 | 待测试 | 批量操作测试 |
| 15 | 搜索待办事宜 | 在搜索框输入关键字搜索 | 显示包含关键字的事宜 | 待测试 | 搜索功能测试 |
| 16 | 查看事宜详情 | 点击待办事宜查看详细信息 | 显示事宜的完整详情 | 待测试 | 详情查看测试 |
| 17 | 设置事宜提醒 | 为重要事宜设置提醒 | 提醒设置成功 | 待测试 | 提醒功能测试 |
| 18 | 删除已完成事宜 | 删除已完成的待办事宜 | 事宜从列表中移除 | 待测试 | 删除功能测试 |
| 19 | 导出待办列表 | 导出待办事宜列表到文件 | 列表导出成功 | 待测试 | 导出功能测试 |
| 20 | 刷新待办列表 | 点击刷新按钮更新列表 | 列表内容更新到最新状态 | 待测试 | 刷新功能测试 |

## 事宜处理流程测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 21 | 处理学生申请 | 从待办列表进入申请审核页面 | 可以直接审核学生申请 | 待测试 | 流程集成测试 |
| 22 | 评阅学生答卷 | 从待办列表进入答卷评阅页面 | 可以直接评阅学生答卷 | 待测试 | 流程集成测试 |
| 23 | 回复重要消息 | 从待办列表进入消息回复页面 | 可以直接回复消息 | 待测试 | 流程集成测试 |
| 24 | 发布考试成绩 | 从待办列表进入成绩发布页面 | 可以直接发布成绩 | 待测试 | 流程集成测试 |
| 25 | 处理系统通知 | 查看并确认系统通知 | 通知状态变为已读 | 待测试 | 通知处理测试 |

## 提醒和通知测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 26 | 新事宜通知 | 有新的待办事宜时的通知 | 收到新事宜通知提醒 | 待测试 | 通知功能测试 |
| 27 | 紧急事宜提醒 | 紧急事宜的特殊提醒 | 收到紧急事宜特殊提醒 | 待测试 | 紧急提醒测试 |
| 28 | 逾期事宜警告 | 事宜逾期时的警告提醒 | 收到逾期警告通知 | 待测试 | 逾期提醒测试 |
| 29 | 定时提醒设置 | 设置定时提醒功能 | 按设定时间收到提醒 | 待测试 | 定时提醒测试 |
| 30 | 提醒方式设置 | 设置不同的提醒方式 | 按设定方式收到提醒 | 待测试 | 提醒方式测试 |

## 状态转换测试

### 待办事宜状态转换图
```
新建 → 未处理 → 处理中 → 已完成
     ↓         ↓
     已取消 ← 已逾期
```

| 编号 | 状态转换 | 测试条件 | 预期结果 | 测试方法 |
|------|---------|---------|---------|---------|
| 31 | 新建→未处理 | 系统自动创建待办事宜 | 状态为未处理 | 状态转换测试 |
| 32 | 未处理→处理中 | 开始处理事宜 | 状态变为处理中 | 状态转换测试 |
| 33 | 处理中→已完成 | 完成事宜处理 | 状态变为已完成 | 状态转换测试 |
| 34 | 未处理→已逾期 | 超过处理期限 | 状态变为已逾期 | 状态转换测试 |
| 35 | 任意状态→已取消 | 取消事宜 | 状态变为已取消 | 状态转换测试 |

## 性能和容量测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 36 | 大量待办事宜显示 | 显示100+个待办事宜 | 页面正常加载，性能良好 | 待测试 | 性能测试 |
| 37 | 待办事宜分页 | 测试待办事宜的分页功能 | 分页正常工作 | 待测试 | 分页功能测试 |
| 38 | 实时更新测试 | 测试待办列表的实时更新 | 新事宜及时显示 | 待测试 | 实时更新测试 |
| 39 | 并发访问测试 | 多用户同时访问待办中心 | 系统稳定，数据正确 | 待测试 | 并发测试 |
| 40 | 数据同步测试 | 测试多设备间的数据同步 | 数据在各设备间同步 | 待测试 | 同步测试 |

## 测试数据准备
1. **待办事宜**: 准备各种类型的待办事宜数据
2. **用户账号**: 准备教师和学生测试账号
3. **时间数据**: 准备不同时间范围的事宜
4. **优先级数据**: 准备不同优先级的事宜
5. **状态数据**: 准备各种状态的事宜

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari
- 网络: 稳定的互联网连接
- 账号: 有效的教师测试账号
- 权限: 待办事宜查看权限
- 通知: 浏览器通知权限开启
- 时间: 准确的系统时间设置
