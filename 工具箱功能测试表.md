# 工具箱功能测试表

## 模块信息
- **模块编号**: KSK-GJ-001
- **模块名称**: 工具箱
- **功能名称**: 试卷转让、辅助工具集合
- **测试方法**: 等价类划分、边界值分析、功能测试、集成测试

## 功能描述
工具箱提供了一些辅助功能，包括试卷转让、数据导入导出、格式转换等实用工具。帮助用户更高效地管理和使用系统资源。

## 等价类划分

### 试卷转让等价类
- **有效等价类**:
  - E1: 自己创建的试卷
  - E2: 有转让权限的试卷
  - E3: 存在的目标用户
- **无效等价类**:
  - E4: 他人创建的试卷
  - E5: 无转让权限的试卷
  - E6: 不存在的目标用户
  - E7: 已转让的试卷

### 文件格式等价类
- **有效等价类**:
  - E8: Word文档(.doc/.docx)
  - E9: Excel文档(.xls/.xlsx)
  - E10: PDF文档(.pdf)
  - E11: 文本文档(.txt)
- **无效等价类**:
  - E12: 不支持的文件格式
  - E13: 损坏的文件
  - E14: 超大文件(>10MB)

### 数据导入等价类
- **有效等价类**:
  - E15: 标准格式的数据
  - E16: 完整的数据记录
- **无效等价类**:
  - E17: 格式错误的数据
  - E18: 不完整的数据记录
  - E19: 重复的数据

### 用户权限等价类
- **有效等价类**:
  - E20: 有工具使用权限的用户
  - E21: 教师用户
- **无效等价类**:
  - E22: 无权限的用户
  - E23: 学生用户(部分工具)

## 测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 进入工具箱 | 点击"工具箱"菜单进入工具页面 | 显示所有可用的工具列表 | 待测试 | 基本功能测试 |
| 2 | 正常试卷转让 | 选择自己的试卷，输入目标用户，点击"转让" | 试卷转让成功，目标用户获得试卷 | 待测试 | 等价类E1+E3 |
| 3 | 转让他人试卷 | 尝试转让他人创建的试卷 | 提示"无权限转让此试卷" | 待测试 | 等价类E4 |
| 4 | 转让给不存在用户 | 输入不存在的用户名进行转让 | 提示"目标用户不存在" | 待测试 | 等价类E6 |
| 5 | 重复转让试卷 | 转让已经转让过的试卷 | 提示"试卷已转让"或"无权限操作" | 待测试 | 等价类E7 |
| 6 | Word文档导入 | 上传Word格式的试卷文档 | 文档导入成功，内容正确解析 | 待测试 | 等价类E8 |
| 7 | Excel文档导入 | 上传Excel格式的题库文档 | 文档导入成功，数据正确解析 | 待测试 | 等价类E9 |
| 8 | PDF文档处理 | 上传PDF格式的文档 | 文档处理成功或提示不支持 | 待测试 | 等价类E10 |
| 9 | 不支持格式文件 | 上传图片或视频文件 | 提示"不支持的文件格式" | 待测试 | 等价类E12 |
| 10 | 超大文件上传 | 上传超过10MB的文件 | 提示"文件过大"或上传失败 | 待测试 | 等价类E14 |
| 11 | 损坏文件处理 | 上传损坏的文档文件 | 提示"文件损坏"或解析失败 | 待测试 | 等价类E13 |
| 12 | 数据导出功能 | 导出试卷数据到Excel | 数据导出成功，格式正确 | 待测试 | 导出功能测试 |
| 13 | 批量数据导入 | 批量导入学生信息或题目 | 数据批量导入成功 | 待测试 | 等价类E15+E16 |
| 14 | 格式错误数据导入 | 导入格式错误的数据 | 提示"数据格式错误"，导入失败 | 待测试 | 等价类E17 |
| 15 | 重复数据处理 | 导入包含重复记录的数据 | 系统识别重复数据，提供处理选项 | 待测试 | 等价类E19 |
| 16 | 数据格式转换 | 将Word试卷转换为系统格式 | 转换成功，格式正确 | 待测试 | 格式转换测试 |
| 17 | 试卷备份功能 | 备份重要试卷数据 | 备份创建成功 | 待测试 | 备份功能测试 |
| 18 | 试卷恢复功能 | 从备份恢复试卷数据 | 数据恢复成功，内容完整 | 待测试 | 恢复功能测试 |
| 19 | 权限验证-学生用户 | 学生用户尝试使用教师工具 | 提示"权限不足"或功能不可见 | 待测试 | 等价类E22+E23 |
| 20 | 工具使用记录 | 查看工具使用历史记录 | 显示工具使用的历史记录 | 待测试 | 记录查看测试 |

## 试卷转让专项测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 21 | 转让试卷权限验证 | 验证转让后的试卷权限归属 | 目标用户获得完整权限 | 待测试 | 权限验证测试 |
| 22 | 转让试卷通知 | 试卷转让后的通知机制 | 双方都收到转让通知 | 待测试 | 通知功能测试 |
| 23 | 转让历史记录 | 查看试卷转让历史 | 显示完整的转让记录 | 待测试 | 历史记录测试 |
| 24 | 批量试卷转让 | 一次转让多个试卷 | 所有试卷转让成功 | 待测试 | 批量操作测试 |
| 25 | 转让确认机制 | 转让前的确认步骤 | 需要确认才能完成转让 | 待测试 | 确认机制测试 |

## 数据处理工具测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 26 | 成绩数据统计 | 使用工具统计班级成绩 | 生成准确的统计报表 | 待测试 | 统计功能测试 |
| 27 | 学生信息管理 | 批量管理学生信息 | 学生信息更新成功 | 待测试 | 信息管理测试 |
| 28 | 题库整理工具 | 整理和分类题库 | 题库整理完成，分类正确 | 待测试 | 整理功能测试 |
| 29 | 重复题目检测 | 检测题库中的重复题目 | 准确识别重复题目 | 待测试 | 检测功能测试 |
| 30 | 数据清理工具 | 清理无效或过期数据 | 无效数据清理成功 | 待测试 | 清理功能测试 |

## 文件处理工具测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 31 | 文件压缩功能 | 压缩大文件以便传输 | 文件压缩成功，大小减小 | 待测试 | 压缩功能测试 |
| 32 | 文件解压功能 | 解压压缩文件 | 文件解压成功，内容完整 | 待测试 | 解压功能测试 |
| 33 | 文件格式识别 | 自动识别文件格式 | 正确识别文件类型 | 待测试 | 识别功能测试 |
| 34 | 文件预览功能 | 预览上传的文件内容 | 文件内容正确显示 | 待测试 | 预览功能测试 |
| 35 | 文件安全检查 | 检查上传文件的安全性 | 识别并阻止恶意文件 | 待测试 | 安全检查测试 |

## 边界值测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 36 | 文件大小边界-最大 | 上传接近10MB的文件 | 文件上传成功 | 待测试 | 边界值分析 |
| 37 | 文件大小边界-超限 | 上传10.1MB的文件 | 提示文件过大，上传失败 | 待测试 | 边界值分析 |
| 38 | 批量操作边界-最大 | 一次处理100个文件 | 所有文件处理成功 | 待测试 | 边界值分析 |
| 39 | 用户名长度边界 | 转让给用户名最长的用户 | 转让成功 | 待测试 | 边界值分析 |
| 40 | 并发操作测试 | 多用户同时使用工具 | 系统稳定，操作正确 | 待测试 | 并发测试 |

## 测试数据准备
1. **试卷数据**: 准备各种类型和状态的试卷
2. **用户账号**: 准备不同权限级别的用户账号
3. **文件样本**: 准备各种格式和大小的测试文件
4. **数据样本**: 准备标准和异常的数据样本
5. **权限配置**: 准备不同的权限配置场景

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari (支持文件上传)
- 网络: 稳定的互联网连接
- 账号: 有效的教师测试账号
- 权限: 工具箱使用权限
- 存储: 足够的文件存储空间
- 软件: 支持各种文件格式的查看器
