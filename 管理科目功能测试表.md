# 管理科目功能测试表

## 模块信息
- **模块编号**: KSK-GL-001
- **模块名称**: 管理科目
- **功能名称**: 设置我感兴趣的科目
- **测试方法**: 等价类划分、边界值分析、决策表

## 功能描述
教师可以设置自己感兴趣的科目，以科目为单位来管理试卷和考试。每个用户独立地设置自己感兴趣的科目，完全实现个性化。

## 等价类划分

### 科目名称等价类
- **有效等价类**:
  - E1: 有效的科目名称(1-50个字符，包含汉字、字母、数字)
  - E2: 系统预设的科目名称
- **无效等价类**:
  - E3: 空科目名称
  - E4: 超长科目名称(>50个字符)
  - E5: 包含特殊字符的科目名称
  - E6: 已存在的科目名称

### 科目描述等价类
- **有效等价类**:
  - E7: 有效的科目描述(0-200个字符)
- **无效等价类**:
  - E8: 超长科目描述(>200个字符)

### 科目分类等价类
- **有效等价类**:
  - E9: 学历类
  - E10: 职业资格类
  - E11: 外语类
  - E12: 计算机类
  - E13: 财会类
  - E14: 建筑类
  - E15: 医药类
  - E16: 公务员类
  - E17: 自定义分类
- **无效等价类**:
  - E18: 未选择分类

## 测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 添加新科目-数学 | 输入科目名称"高中数学"、描述"高中数学课程"、选择"学历类"分类，点击"添加" | 科目添加成功，显示在科目列表中 | 待测试 | 等价类E1+E7+E9 |
| 2 | 添加系统预设科目 | 从系统预设科目列表中选择"英语四级"，点击添加 | 科目添加成功，自动填充相关信息 | 待测试 | 等价类E2 |
| 3 | 科目名称为空 | 科目名称留空，填写描述和分类，点击添加 | 提示"请输入科目名称" | 待测试 | 等价类E3 |
| 4 | 科目名称过长 | 输入51个字符的科目名称，其他信息正确 | 提示"科目名称过长"或自动截断 | 待测试 | 等价类E4 |
| 5 | 科目名称包含特殊字符 | 输入"数学@#$%"，其他信息正确 | 提示"科目名称包含非法字符"或添加成功 | 待测试 | 等价类E5 |
| 6 | 重复添加同名科目 | 添加已存在的科目名称，其他信息正确 | 提示"科目已存在"或自动合并 | 待测试 | 等价类E6 |
| 7 | 科目描述过长 | 输入201个字符的描述，其他信息正确 | 提示"描述过长"或自动截断 | 待测试 | 等价类E8 |
| 8 | 未选择科目分类 | 不选择科目分类，其他信息正确 | 提示"请选择科目分类"或使用默认分类 | 待测试 | 等价类E18 |
| 9 | 科目名称边界值-最短 | 输入1个字符"数"，其他信息正确 | 科目添加成功 | 待测试 | 边界值分析 |
| 10 | 科目名称边界值-最长 | 输入50个字符的科目名称，其他信息正确 | 科目添加成功 | 待测试 | 边界值分析 |
| 11 | 科目描述边界值-最长 | 输入200个字符的描述，其他信息正确 | 科目添加成功 | 待测试 | 边界值分析 |
| 12 | 空描述添加科目 | 科目描述留空，其他信息正确 | 科目添加成功 | 待测试 | 边界值分析 |
| 13 | 修改科目信息 | 修改已添加科目的名称和描述 | 科目信息更新成功 | 待测试 | 编辑功能测试 |
| 14 | 删除科目 | 删除已添加的科目 | 科目删除成功，相关试卷处理提示 | 待测试 | 删除功能测试 |
| 15 | 查看科目列表 | 进入科目管理页面查看所有科目 | 显示所有已添加的科目列表 | 待测试 | 查看功能测试 |
| 16 | 科目排序 | 对科目列表进行排序操作 | 科目按指定顺序排列 | 待测试 | 排序功能测试 |
| 17 | 科目搜索 | 在科目列表中搜索特定科目 | 显示匹配的科目 | 待测试 | 搜索功能测试 |
| 18 | 批量添加科目 | 一次性添加多个科目 | 所有科目添加成功 | 待测试 | 批量操作测试 |
| 19 | 科目分类筛选 | 按分类筛选科目列表 | 只显示指定分类的科目 | 待测试 | 筛选功能测试 |
| 20 | 科目统计信息 | 查看科目下的试卷和考试统计 | 显示准确的统计数据 | 待测试 | 统计功能测试 |

## 科目分类测试用例

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 21 | 添加学历类科目 | 选择"学历类"分类，添加"小学语文" | 科目添加到学历类分类下 | 待测试 | 分类测试 |
| 22 | 添加职业资格类科目 | 选择"职业资格类"分类，添加"会计师考试" | 科目添加到职业资格类分类下 | 待测试 | 分类测试 |
| 23 | 添加外语类科目 | 选择"外语类"分类，添加"日语N1" | 科目添加到外语类分类下 | 待测试 | 分类测试 |
| 24 | 添加计算机类科目 | 选择"计算机类"分类，添加"Java程序设计" | 科目添加到计算机类分类下 | 待测试 | 分类测试 |
| 25 | 添加自定义分类科目 | 创建新分类"兴趣爱好"，添加科目 | 新分类创建成功，科目添加成功 | 待测试 | 自定义分类测试 |

## 决策表分析

### 添加科目决策表
| 条件 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 科目名称有效 | Y | Y | Y | Y | N | N | N | N |
| 科目描述有效 | Y | Y | N | N | Y | Y | N | N |
| 分类已选择 | Y | N | Y | N | Y | N | Y | N |

| 动作 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 添加成功 | X | | | | | | | |
| 提示选择分类 | | X | | X | | X | | X |
| 提示描述无效 | | | X | X | | | X | X |
| 提示名称无效 | | | | | X | X | X | X |

## 科目管理高级功能测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 26 | 科目导入功能 | 从文件导入科目列表 | 科目批量导入成功 | 待测试 | 导入功能测试 |
| 27 | 科目导出功能 | 导出科目列表到文件 | 科目导出成功 | 待测试 | 导出功能测试 |
| 28 | 科目备份功能 | 备份科目数据 | 科目数据备份成功 | 待测试 | 备份功能测试 |
| 29 | 科目恢复功能 | 从备份恢复科目数据 | 科目数据恢复成功 | 待测试 | 恢复功能测试 |
| 30 | 科目权限设置 | 设置科目的访问权限 | 权限设置生效 | 待测试 | 权限管理测试 |

## 测试数据准备
1. **科目名称**: 准备各种长度和格式的科目名称
2. **科目分类**: 准备所有系统支持的分类
3. **科目描述**: 准备不同长度的描述内容
4. **边界值数据**: 准备临界长度的输入数据
5. **特殊字符**: 准备包含各种特殊字符的测试数据

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari
- 网络: 稳定的互联网连接
- 账号: 有效的教师测试账号
- 权限: 科目管理权限
