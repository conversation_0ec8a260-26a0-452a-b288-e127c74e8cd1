# 考试酷网站软件测试计划表

## 测试对象
- **被测网站**: 考试酷 (https://www.examcoo.com/)
- **测试类型**: 黑盒测试
- **测试方法**: 等价类划分、边界值分析、因果图、决策表

## 测试计划表

| 模块编号 | 模块名称 | 功能名称 | 需求优先级 |
|---------|---------|---------|-----------|
| KSK-DL-001 | 登录 | 用户登录 | 高 |
| KSK-ZC-001 | 注册 | 学生注册 | 高 |
| KSK-ZC-002 | 注册 | 教师注册 | 高 |
| KSK-LS-001 | 教师-我的班级 | 创建班级 | 高 |
| KSK-LS-002 | 教师-我的班级 | 申请加入班级 | 高 |
| KSK-XX-001 | 教师-我的消息 | 发件箱 | 高 |
| KSK-XX-002 | 教师-我的消息 | 收件箱 | 高 |
| KSK-SJ-001 | 教师-试卷管理 | 录入试卷 | 中 |
| KSK-SJ-002 | 教师-试卷管理 | 导入试卷 | 中 |
| KSK-SJ-003 | 教师-试卷管理 | 试卷编辑 | 中 |
| KSK-KS-001 | 教师-考试管理 | 组织班级考试 | 中 |
| KSK-KS-002 | 教师-考试管理 | 布置电子作业 | 中 |
| KSK-PJ-001 | 教师-评卷管理 | 手工阅卷 | 中 |
| KSK-PJ-002 | 教师-评卷管理 | 成绩管理 | 中 |
| KSK-XS-001 | 学生-考试功能 | 参加考试 | 中 |
| KSK-XS-002 | 学生-考试功能 | 查看成绩 | 中 |
| KSK-XS-003 | 学生-考试功能 | 自测练习 | 低 |
| KSK-GL-001 | 个人管理 | 个人信息修改 | 低 |
| KSK-GL-002 | 个人管理 | 密码修改 | 低 |

## 测试优先级说明

### 高优先级功能
- **登录功能**: 系统入口，必须优先测试
- **注册功能**: 用户获取系统访问权限的基础功能
- **班级管理**: 教师核心功能，影响后续所有教学活动
- **消息功能**: 师生沟通的重要渠道

### 中优先级功能
- **试卷管理**: 教师日常教学工作的重要功能
- **考试管理**: 系统核心业务功能
- **评卷管理**: 考试流程的重要环节
- **学生考试功能**: 学生端核心功能

### 低优先级功能
- **个人管理**: 辅助功能，不影响核心业务流程
- **自测练习**: 可选功能，优先级较低

## 测试范围说明
根据作业要求和时间限制，本次测试将重点关注高优先级功能模块，确保系统核心功能的质量。中优先级功能根据时间情况选择性测试，低优先级功能暂不纳入本次测试范围。

## 测试环境
- **操作系统**: Windows 10/11, macOS, Linux
- **浏览器**: Chrome, Firefox, Safari, Edge
- **网络环境**: 宽带网络连接
- **测试数据**: 准备有效和无效的测试数据集

## 测试方法应用说明
1. **等价类划分**: 对输入数据进行分类，设计代表性测试用例
2. **边界值分析**: 重点测试输入域边界附近的值
3. **因果图**: 分析输入条件与输出结果的逻辑关系
4. **决策表**: 处理复杂的业务逻辑组合情况
