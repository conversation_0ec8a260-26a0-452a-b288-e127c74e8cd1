# 申请加入班级功能测试表

## 模块信息
- **模块编号**: KSK-BJ-002
- **模块名称**: 申请加入班级
- **功能名称**: 学生申请加入班级
- **测试方法**: 等价类划分、边界值分析、决策表、状态转换

## 功能描述
学生可以申请加入班级。班级创建者和管理员可以审核申请，同意或拒绝学生加入班级。支持搜索班级、查看班级信息、提交申请等功能。

## 等价类划分

### 班级搜索等价类
- **有效等价类**:
  - E1: 存在的班级名称
  - E2: 存在的班级ID
  - E3: 班级关键字
- **无效等价类**:
  - E4: 不存在的班级名称
  - E5: 空搜索条件
  - E6: 无效的班级ID

### 班级状态等价类
- **有效等价类**:
  - E7: 公开班级(可申请)
  - E8: 私有班级(需邀请)
  - E9: 活跃班级
- **无效等价类**:
  - E10: 已满员班级
  - E11: 已结束班级
  - E12: 已解散班级

### 申请状态等价类
- **有效等价类**:
  - E13: 可申请状态(未申请过)
- **无效等价类**:
  - E14: 已申请待审核
  - E15: 已是班级成员
  - E16: 申请被拒绝

### 申请理由等价类
- **有效等价类**:
  - E17: 有效的申请理由(1-200个字符)
- **无效等价类**:
  - E18: 空申请理由
  - E19: 超长申请理由(>200个字符)

## 测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 搜索公开班级 | 输入存在的公开班级名称"高一数学班"进行搜索 | 显示匹配的班级信息 | 待测试 | 等价类E1+E7 |
| 2 | 正常申请加入班级 | 搜索到公开班级，填写申请理由"希望提高数学成绩"，点击"申请加入" | 申请提交成功，等待管理员审核 | 待测试 | 等价类E7+E13+E17 |
| 3 | 搜索不存在的班级 | 输入不存在的班级名称"不存在的班级"进行搜索 | 提示"未找到相关班级" | 待测试 | 等价类E4 |
| 4 | 空搜索条件 | 不输入任何搜索条件直接点击搜索 | 提示"请输入搜索条件"或显示所有公开班级 | 待测试 | 等价类E5 |
| 5 | 申请加入私有班级 | 尝试申请加入私有班级 | 提示"该班级不接受申请"或申请失败 | 待测试 | 等价类E8 |
| 6 | 重复申请同一班级 | 对已申请的班级再次申请 | 提示"已申请该班级"或"申请正在审核中" | 待测试 | 等价类E14 |
| 7 | 申请加入已加入的班级 | 对已加入的班级申请加入 | 提示"已是该班级成员" | 待测试 | 等价类E15 |
| 8 | 申请加入已满员班级 | 申请加入人数已满的班级 | 提示"班级人数已满，无法申请" | 待测试 | 等价类E10 |
| 9 | 申请理由为空 | 不填写申请理由直接申请 | 提示"请填写申请理由"或使用默认理由 | 待测试 | 等价类E18 |
| 10 | 申请理由过长 | 填写201个字符的申请理由 | 提示"申请理由过长"或自动截断 | 待测试 | 等价类E19 |
| 11 | 班级搜索-精确匹配 | 输入完整的班级名称搜索 | 显示精确匹配的班级 | 待测试 | 搜索功能测试 |
| 12 | 班级搜索-模糊匹配 | 输入班级名称的部分关键字 | 显示包含关键字的班级列表 | 待测试 | 搜索功能测试 |
| 13 | 班级搜索-特殊字符 | 输入包含特殊字符的搜索条件 | 系统正常处理，不出现错误 | 待测试 | 异常处理测试 |
| 14 | 查看班级详情 | 点击班级名称查看班级详细信息 | 显示班级的详细信息页面 | 待测试 | 详情查看测试 |
| 15 | 申请理由边界值-最短 | 填写1个字符的申请理由"好" | 申请提交成功 | 待测试 | 边界值分析 |
| 16 | 申请理由边界值-最长 | 填写200个字符的申请理由 | 申请提交成功 | 待测试 | 边界值分析 |
| 17 | 批量申请多个班级 | 同时申请加入多个不同的班级 | 所有申请都提交成功 | 待测试 | 批量操作测试 |
| 18 | 撤销申请 | 撤销已提交但未审核的申请 | 申请撤销成功 | 待测试 | 撤销功能测试 |
| 19 | 查看申请状态 | 查看已提交申请的审核状态 | 显示申请的当前状态 | 待测试 | 状态查看测试 |
| 20 | 申请被拒绝后重新申请 | 在申请被拒绝后重新申请同一班级 | 可以重新提交申请 | 待测试 | 状态转换测试 |

## 班级筛选和排序测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 21 | 按班级类型筛选 | 筛选显示"学历教育类"班级 | 只显示学历教育类的班级 | 待测试 | 筛选功能测试 |
| 22 | 按人数筛选 | 筛选显示人数少于20人的班级 | 显示符合条件的班级 | 待测试 | 筛选功能测试 |
| 23 | 按创建时间排序 | 按班级创建时间排序 | 班级按时间顺序排列 | 待测试 | 排序功能测试 |
| 24 | 按成员数量排序 | 按班级成员数量排序 | 班级按成员数量排列 | 待测试 | 排序功能测试 |
| 25 | 综合筛选 | 同时使用多个筛选条件 | 显示同时满足所有条件的班级 | 待测试 | 复合筛选测试 |

## 申请审核流程测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 26 | 管理员同意申请 | 班级管理员审核并同意学生申请 | 学生成功加入班级，收到通知 | 待测试 | 审核流程测试 |
| 27 | 管理员拒绝申请 | 班级管理员审核并拒绝学生申请 | 申请被拒绝，学生收到拒绝通知 | 待测试 | 审核流程测试 |
| 28 | 申请超时处理 | 申请长时间未审核的处理 | 系统自动处理或提醒管理员 | 待测试 | 超时处理测试 |
| 29 | 批量审核申请 | 管理员批量处理多个申请 | 所有申请都得到处理 | 待测试 | 批量审核测试 |
| 30 | 申请通知功能 | 申请状态变化时的通知 | 相关人员收到状态变化通知 | 待测试 | 通知功能测试 |

## 决策表分析

### 申请加入班级决策表
| 条件 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 班级存在 | Y | Y | Y | Y | N | N | N | N |
| 班级公开 | Y | Y | N | N | Y | Y | N | N |
| 未加入该班级 | Y | N | Y | N | Y | N | Y | N |

| 动作 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 申请成功 | X | | | | | | | |
| 提示已是成员 | | X | | X | | X | | X |
| 提示班级私有 | | | X | X | | | X | X |
| 提示班级不存在 | | | | | X | X | X | X |

## 状态转换测试

### 申请状态转换图
```
未申请 → 申请中 → 申请成功(成为成员)
       ↓
       申请被拒绝 → 重新申请
```

| 编号 | 状态转换 | 测试条件 | 预期结果 | 测试方法 |
|------|---------|---------|---------|---------|
| 31 | 未申请→申请中 | 提交申请 | 状态变为申请中 | 状态转换测试 |
| 32 | 申请中→申请成功 | 管理员同意 | 成为班级成员 | 状态转换测试 |
| 33 | 申请中→申请被拒绝 | 管理员拒绝 | 状态变为被拒绝 | 状态转换测试 |
| 34 | 申请被拒绝→重新申请 | 重新提交申请 | 可以再次申请 | 状态转换测试 |
| 35 | 申请中→撤销申请 | 主动撤销 | 状态变为未申请 | 状态转换测试 |

## 测试数据准备
1. **班级数据**: 准备公开、私有、满员、正常等各种状态的班级
2. **用户账号**: 准备学生和教师测试账号
3. **申请理由**: 准备各种长度的申请理由文本
4. **搜索关键字**: 准备有效和无效的搜索条件
5. **权限数据**: 准备不同权限级别的测试账号

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari
- 网络: 稳定的互联网连接
- 账号: 有效的学生和教师测试账号
- 数据库: 包含测试班级数据
- 通知: 支持系统通知功能
