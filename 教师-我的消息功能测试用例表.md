# 教师-我的消息功能测试用例表

## 模块信息
- **模块编号**: KSK-XX-001, KSK-XX-002
- **模块名称**: 教师-我的消息
- **功能名称**: 发件箱、收件箱
- **测试方法**: 等价类划分、边界值分析、因果图、决策表

## 功能1: 发件箱

### 等价类划分

#### 收件人等价类
- **有效等价类**:
  - E1: 存在的用户名或邮箱
  - E2: 班级成员用户名
- **无效等价类**:
  - E3: 不存在的用户名
  - E4: 空收件人
  - E5: 格式错误的邮箱地址

#### 消息主题等价类
- **有效等价类**:
  - E6: 有效的消息主题(1-100个字符)
- **无效等价类**:
  - E7: 空主题
  - E8: 超长主题(>100个字符)

#### 消息内容等价类
- **有效等价类**:
  - E9: 有效的消息内容(1-2000个字符)
- **无效等价类**:
  - E10: 空内容
  - E11: 超长内容(>2000个字符)

### 发件箱测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 正常发送消息 | 输入有效收件人、主题"作业通知"、内容"请按时完成作业"，点击"发送" | 消息发送成功，显示在发件箱中 | 待测试 | 等价类E1+E6+E9 |
| 2 | 发送给班级成员 | 选择班级成员作为收件人，填写主题和内容，发送 | 消息发送成功，班级成员收到消息 | 待测试 | 等价类E2+E6+E9 |
| 3 | 收件人为空 | 收件人留空，填写主题和内容，点击发送 | 提示"请输入收件人" | 待测试 | 等价类E4 |
| 4 | 主题为空 | 输入收件人和内容，主题留空，点击发送 | 提示"请输入主题"或使用默认主题 | 待测试 | 等价类E7 |
| 5 | 内容为空 | 输入收件人和主题，内容留空，点击发送 | 提示"请输入消息内容" | 待测试 | 等价类E10 |
| 6 | 收件人不存在 | 输入不存在的用户名，填写主题和内容，发送 | 提示"收件人不存在" | 待测试 | 等价类E3 |
| 7 | 主题过长 | 输入101个字符的主题，其他信息正确 | 提示"主题过长"或自动截断 | 待测试 | 等价类E8 |
| 8 | 内容过长 | 输入2001个字符的内容，其他信息正确 | 提示"内容过长"或自动截断 | 待测试 | 等价类E11 |
| 9 | 主题边界值-最短 | 输入1个字符主题"A"，其他信息正确 | 消息发送成功 | 待测试 | 边界值分析 |
| 10 | 主题边界值-最长 | 输入100个字符的主题，其他信息正确 | 消息发送成功 | 待测试 | 边界值分析 |
| 11 | 内容边界值-最短 | 输入1个字符内容"好"，其他信息正确 | 消息发送成功 | 待测试 | 边界值分析 |
| 12 | 内容边界值-最长 | 输入2000个字符的内容，其他信息正确 | 消息发送成功 | 待测试 | 边界值分析 |
| 13 | 批量发送消息 | 选择多个收件人，发送同一消息 | 所有收件人都收到消息 | 待测试 | 批量操作测试 |
| 14 | 发送带附件消息 | 添加附件后发送消息 | 消息和附件都发送成功 | 待测试 | 附件功能测试 |
| 15 | 发送紧急消息 | 标记消息为紧急，发送给收件人 | 消息以紧急标识发送 | 待测试 | 优先级测试 |

## 功能2: 收件箱

### 等价类划分

#### 消息状态等价类
- **有效等价类**:
  - E12: 未读消息
  - E13: 已读消息
- **无效等价类**:
  - E14: 已删除消息

#### 消息操作等价类
- **有效等价类**:
  - E15: 可读取的消息
  - E16: 可回复的消息
  - E17: 可删除的消息
- **无效等价类**:
  - E18: 损坏的消息

### 收件箱测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 16 | 查看收件箱列表 | 登录后进入收件箱 | 显示所有接收到的消息列表 | 待测试 | 基本功能测试 |
| 17 | 读取未读消息 | 点击未读消息查看详情 | 消息内容显示，状态变为已读 | 待测试 | 等价类E12+E15 |
| 18 | 读取已读消息 | 点击已读消息查看详情 | 消息内容正常显示 | 待测试 | 等价类E13+E15 |
| 19 | 回复消息 | 在消息详情页点击"回复"，填写回复内容 | 回复消息发送成功 | 待测试 | 等价类E16 |
| 20 | 删除单条消息 | 选择一条消息，点击删除 | 消息被删除，不再显示在收件箱 | 待测试 | 等价类E17 |
| 21 | 批量删除消息 | 选择多条消息，批量删除 | 所选消息都被删除 | 待测试 | 批量操作测试 |
| 22 | 标记为已读 | 选择未读消息，标记为已读 | 消息状态变为已读 | 待测试 | 状态管理测试 |
| 23 | 标记为未读 | 选择已读消息，标记为未读 | 消息状态变为未读 | 待测试 | 状态管理测试 |
| 24 | 搜索消息 | 在搜索框输入关键字搜索消息 | 显示包含关键字的消息 | 待测试 | 搜索功能测试 |
| 25 | 按发件人筛选 | 选择特定发件人筛选消息 | 只显示该发件人的消息 | 待测试 | 筛选功能测试 |
| 26 | 按时间排序 | 按发送时间对消息排序 | 消息按时间顺序排列 | 待测试 | 排序功能测试 |
| 27 | 查看消息详情 | 点击消息查看完整内容 | 显示消息的所有详细信息 | 待测试 | 详情查看测试 |
| 28 | 转发消息 | 选择消息进行转发操作 | 消息转发给指定收件人 | 待测试 | 转发功能测试 |
| 29 | 收件箱容量测试 | 接收大量消息测试收件箱容量 | 系统正常处理，不出现异常 | 待测试 | 容量测试 |
| 30 | 消息通知功能 | 接收新消息时的通知提醒 | 收到新消息通知提醒 | 待测试 | 通知功能测试 |

## 功能3: 消息管理

### 消息管理测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 31 | 创建消息文件夹 | 创建新的消息分类文件夹 | 文件夹创建成功 | 待测试 | 分类管理测试 |
| 32 | 移动消息到文件夹 | 将消息移动到指定文件夹 | 消息移动成功 | 待测试 | 分类管理测试 |
| 33 | 设置消息过滤规则 | 设置自动分类规则 | 新消息按规则自动分类 | 待测试 | 自动化功能测试 |
| 34 | 导出消息 | 导出消息到本地文件 | 消息导出成功 | 待测试 | 导出功能测试 |
| 35 | 消息备份 | 备份重要消息 | 消息备份成功 | 待测试 | 备份功能测试 |

## 因果图分析

### 发送消息因果图
- **输入条件**:
  - C1: 收件人有效
  - C2: 主题有效
  - C3: 内容有效
- **输出结果**:
  - E1: 发送成功
  - E2: 发送失败

### 逻辑关系
- 发送成功: C1 AND C2 AND C3 → E1
- 发送失败: NOT(C1 AND C2 AND C3) → E2

## 决策表分析

### 发送消息决策表
| 条件 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 收件人有效 | Y | Y | Y | Y | N | N | N | N |
| 主题有效 | Y | Y | N | N | Y | Y | N | N |
| 内容有效 | Y | N | Y | N | Y | N | Y | N |

| 动作 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 发送成功 | X | | | | | | | |
| 提示内容无效 | | X | | X | | X | | X |
| 提示主题无效 | | | X | X | | | X | X |
| 提示收件人无效 | | | | | X | X | X | X |

## 测试数据准备
1. **用户账号**: 准备发送方和接收方测试账号
2. **消息内容**: 准备各种长度和格式的消息内容
3. **收件人列表**: 准备有效和无效的收件人数据
4. **附件文件**: 准备不同类型和大小的附件
5. **搜索关键字**: 准备用于搜索测试的关键字

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari
- 网络: 稳定的互联网连接
- 账号: 多个有效的测试账号
- 存储: 足够的消息存储空间
- 通知: 浏览器通知权限开启
