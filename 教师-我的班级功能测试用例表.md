# 教师-我的班级功能测试用例表

## 模块信息
- **模块编号**: KSK-LS-001, KSK-LS-002
- **模块名称**: 教师-我的班级
- **功能名称**: 创建班级、申请加入班级
- **测试方法**: 等价类划分、边界值分析、因果图、决策表

## 功能1: 创建班级

### 等价类划分

#### 班级名称等价类
- **有效等价类**:
  - E1: 有效的班级名称(1-50个字符，包含汉字、字母、数字)
- **无效等价类**:
  - E2: 空班级名称
  - E3: 超长班级名称(>50个字符)
  - E4: 包含特殊字符的班级名称
  - E5: 已存在的班级名称

#### 班级描述等价类
- **有效等价类**:
  - E6: 有效的班级描述(0-200个字符)
- **无效等价类**:
  - E7: 超长班级描述(>200个字符)

#### 班级类型等价类
- **有效等价类**:
  - E8: 公开班级
  - E9: 私有班级
- **无效等价类**:
  - E10: 未选择班级类型

### 创建班级测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 正常创建公开班级 | 输入有效班级名称"高一数学班"、描述"高一年级数学课程"、选择公开类型，点击"创建" | 班级创建成功，显示在班级列表中 | 待测试 | 等价类E1+E6+E8 |
| 2 | 正常创建私有班级 | 输入有效班级名称"VIP辅导班"、描述"小班制辅导"、选择私有类型，点击"创建" | 班级创建成功，仅创建者可见 | 待测试 | 等价类E1+E6+E9 |
| 3 | 班级名称为空 | 班级名称留空，其他信息正确填写 | 提示"请输入班级名称" | 待测试 | 等价类E2 |
| 4 | 班级名称过长 | 输入51个字符的班级名称，其他信息正确 | 提示"班级名称过长"或自动截断 | 待测试 | 等价类E3 |
| 5 | 班级名称包含特殊字符 | 输入"数学班@#$%"，其他信息正确 | 提示"班级名称包含非法字符"或创建成功 | 待测试 | 等价类E4 |
| 6 | 班级名称已存在 | 输入已存在的班级名称，其他信息正确 | 提示"班级名称已存在" | 待测试 | 等价类E5 |
| 7 | 班级描述过长 | 输入201个字符的描述，其他信息正确 | 提示"描述过长"或自动截断 | 待测试 | 等价类E7 |
| 8 | 未选择班级类型 | 不选择班级类型，其他信息正确 | 提示"请选择班级类型"或使用默认类型 | 待测试 | 等价类E10 |
| 9 | 班级名称边界值-最短 | 输入1个字符"A"，其他信息正确 | 班级创建成功 | 待测试 | 边界值分析 |
| 10 | 班级名称边界值-最长 | 输入50个字符的班级名称，其他信息正确 | 班级创建成功 | 待测试 | 边界值分析 |
| 11 | 班级描述边界值-最长 | 输入200个字符的描述，其他信息正确 | 班级创建成功 | 待测试 | 边界值分析 |
| 12 | 空描述创建班级 | 班级描述留空，其他信息正确 | 班级创建成功 | 待测试 | 边界值分析 |
| 13 | 重复创建同名班级 | 创建成功后再次创建同名班级 | 提示"班级名称已存在"或自动添加后缀 | 待测试 | 重复操作测试 |
| 14 | 创建班级权限验证 | 学生账号尝试创建班级 | 提示"权限不足"或功能不可见 | 待测试 | 权限测试 |
| 15 | 批量创建班级 | 连续创建多个班级 | 所有班级创建成功，系统性能正常 | 待测试 | 性能测试 |

## 功能2: 申请加入班级

### 等价类划分

#### 班级搜索等价类
- **有效等价类**:
  - E11: 存在的班级名称或ID
- **无效等价类**:
  - E12: 不存在的班级名称或ID
  - E13: 空搜索条件

#### 申请状态等价类
- **有效等价类**:
  - E14: 可申请加入的班级(公开班级)
- **无效等价类**:
  - E15: 不可申请的班级(私有班级)
  - E16: 已加入的班级
  - E17: 已申请待审核的班级

### 申请加入班级测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 16 | 正常申请加入公开班级 | 搜索存在的公开班级，点击"申请加入" | 申请提交成功，等待管理员审核 | 待测试 | 等价类E11+E14 |
| 17 | 搜索不存在的班级 | 输入不存在的班级名称进行搜索 | 提示"未找到相关班级" | 待测试 | 等价类E12 |
| 18 | 空搜索条件 | 不输入任何搜索条件直接搜索 | 提示"请输入搜索条件"或显示所有公开班级 | 待测试 | 等价类E13 |
| 19 | 申请加入私有班级 | 尝试申请加入私有班级 | 提示"该班级不接受申请"或申请失败 | 待测试 | 等价类E15 |
| 20 | 重复申请同一班级 | 对已申请的班级再次申请 | 提示"已申请该班级"或"申请正在审核中" | 待测试 | 等价类E17 |
| 21 | 申请加入已加入的班级 | 对已加入的班级申请加入 | 提示"已是该班级成员" | 待测试 | 等价类E16 |
| 22 | 班级搜索功能-精确匹配 | 输入完整的班级名称搜索 | 显示匹配的班级 | 待测试 | 搜索功能测试 |
| 23 | 班级搜索功能-模糊匹配 | 输入班级名称的部分关键字 | 显示包含关键字的班级列表 | 待测试 | 搜索功能测试 |
| 24 | 班级搜索功能-特殊字符 | 输入包含特殊字符的搜索条件 | 系统正常处理，不出现错误 | 待测试 | 异常处理测试 |
| 25 | 申请理由填写 | 填写申请理由后申请加入班级 | 申请成功，理由传递给管理员 | 待测试 | 功能完整性测试 |

## 功能3: 班级管理

### 班级管理测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 26 | 查看班级成员列表 | 进入已创建的班级，查看成员列表 | 显示所有班级成员信息 | 待测试 | 功能测试 |
| 27 | 审核加入申请 | 处理学生的加入申请 | 可以同意或拒绝申请 | 待测试 | 审核流程测试 |
| 28 | 移除班级成员 | 选择班级成员进行移除操作 | 成员被移除，收到通知 | 待测试 | 管理功能测试 |
| 29 | 设置班级管理员 | 将班级成员设置为管理员 | 成员获得管理员权限 | 待测试 | 权限管理测试 |
| 30 | 修改班级信息 | 修改班级名称、描述等信息 | 班级信息更新成功 | 待测试 | 编辑功能测试 |
| 31 | 解散班级 | 解散已创建的班级 | 班级被删除，成员收到通知 | 待测试 | 删除功能测试 |
| 32 | 班级公开性切换 | 将私有班级改为公开或相反 | 班级类型切换成功 | 待测试 | 状态切换测试 |

## 因果图分析

### 创建班级因果图
- **原因**: C1(班级名称有效) AND C2(用户是教师) AND C3(名称未重复)
- **结果**: E1(创建成功) OR E2(创建失败)

### 申请加入班级因果图
- **原因**: C4(班级存在) AND C5(班级公开) AND C6(未加入该班级)
- **结果**: E3(申请成功) OR E4(申请失败)

## 决策表分析

### 创建班级决策表
| 条件 | 规则1 | 规则2 | 规则3 | 规则4 |
|------|-------|-------|-------|-------|
| 班级名称有效 | Y | Y | N | N |
| 用户是教师 | Y | N | Y | N |
| 名称未重复 | Y | Y | Y | N |

| 动作 | 规则1 | 规则2 | 规则3 | 规则4 |
|------|-------|-------|-------|-------|
| 创建成功 | X | | | |
| 提示权限不足 | | X | | |
| 提示名称无效 | | | X | |
| 提示名称重复 | | | | X |

## 测试数据准备
1. **班级名称**: 准备各种长度和格式的班级名称
2. **用户账号**: 准备教师和学生测试账号
3. **班级类型**: 准备公开和私有班级测试数据
4. **搜索关键字**: 准备有效和无效的搜索条件
5. **权限测试**: 准备不同权限级别的测试账号

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari
- 网络: 稳定的互联网连接
- 账号: 有效的教师和学生测试账号
- 数据库: 包含测试班级数据
