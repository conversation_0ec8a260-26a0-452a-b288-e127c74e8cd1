# 答卷评阅功能测试表

## 模块信息
- **模块编号**: KSK-PY-001
- **模块名称**: 答卷评阅
- **功能名称**: 手工阅卷、成绩管理
- **测试方法**: 等价类划分、边界值分析、工作流测试、权限测试

## 功能描述
教师可以对学生提交的答卷进行评阅，包括客观题自动评分和主观题手工评分。支持批量评阅、评分标准设置、成绩统计分析等功能。

## 等价类划分

### 答卷状态等价类
- **有效等价类**:
  - E1: 已提交待评阅的答卷
  - E2: 部分评阅的答卷
  - E3: 需要重新评阅的答卷
- **无效等价类**:
  - E4: 未提交的答卷
  - E5: 已评阅完成的答卷
  - E6: 已发布成绩的答卷

### 题目类型等价类
- **有效等价类**:
  - E7: 客观题(单选、多选、判断)
  - E8: 主观题(填空、问答、计算)
  - E9: 半主观题(需要人工确认的填空题)
- **无效等价类**:
  - E10: 无效题型

### 评分等价类
- **有效等价类**:
  - E11: 0分(完全错误)
  - E12: 满分(完全正确)
  - E13: 部分分数(0-满分之间)
- **无效等价类**:
  - E14: 负分
  - E15: 超过满分的分数
  - E16: 非数字分数

### 评阅权限等价类
- **有效等价类**:
  - E17: 试卷创建者
  - E18: 班级管理员
  - E19: 被授权的评阅教师
- **无效等价类**:
  - E20: 学生用户
  - E21: 无权限教师

### 评阅状态等价类
- **有效等价类**:
  - E22: 未开始评阅
  - E23: 评阅中
  - E24: 评阅完成
- **无效等价类**:
  - E25: 评阅异常

## 测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 进入答卷评阅页面 | 点击"答卷评阅"菜单 | 显示待评阅的答卷列表 | 待测试 | 基本功能测试 |
| 2 | 查看待评阅答卷 | 查看学生提交的答卷列表 | 显示所有待评阅答卷及基本信息 | 待测试 | 等价类E1 |
| 3 | 开始评阅答卷 | 点击某个答卷开始评阅 | 进入答卷评阅界面 | 待测试 | 等价类E1+E17 |
| 4 | 客观题自动评分 | 查看客观题的自动评分结果 | 客观题分数自动计算正确 | 待测试 | 等价类E7 |
| 5 | 主观题手工评分 | 对问答题进行手工评分 | 可以输入分数和评语 | 待测试 | 等价类E8 |
| 6 | 填空题评分 | 对填空题进行评分 | 可以选择完全正确、部分正确或错误 | 待测试 | 等价类E9 |
| 7 | 给满分 | 给题目打满分 | 分数设置为题目满分值 | 待测试 | 等价类E12 |
| 8 | 给0分 | 给完全错误的题目打0分 | 分数设置为0 | 待测试 | 等价类E11 |
| 9 | 给部分分数 | 给题目打部分分数 | 分数在0-满分之间 | 待测试 | 等价类E13 |
| 10 | 输入负分 | 尝试输入负数分数 | 提示"分数不能为负数" | 待测试 | 等价类E14 |
| 11 | 输入超分 | 输入超过满分的分数 | 提示"分数不能超过满分" | 待测试 | 等价类E15 |
| 12 | 输入非数字分数 | 输入字母或特殊字符作为分数 | 提示"请输入有效数字" | 待测试 | 等价类E16 |
| 13 | 添加评语 | 为题目添加评阅评语 | 评语保存成功 | 待测试 | 评语功能测试 |
| 14 | 保存评阅进度 | 评阅过程中保存进度 | 评阅进度保存成功 | 待测试 | 保存功能测试 |
| 15 | 提交评阅结果 | 完成评阅后提交结果 | 评阅结果提交成功 | 待测试 | 提交功能测试 |
| 16 | 批量评阅 | 对多个答卷进行批量评阅 | 批量评阅操作成功 | 待测试 | 批量操作测试 |
| 17 | 评分标准设置 | 设置题目的评分标准 | 评分标准设置成功 | 待测试 | 标准设置测试 |
| 18 | 重新评阅 | 对已评阅的答卷重新评阅 | 可以修改之前的评分 | 待测试 | 等价类E3 |
| 19 | 评阅权限验证 | 学生尝试评阅答卷 | 提示"无权限进行评阅" | 待测试 | 等价类E20 |
| 20 | 查看评阅历史 | 查看答卷的评阅历史记录 | 显示完整的评阅历史 | 待测试 | 历史记录测试 |

## 评分细节测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 21 | 分数边界值-0分 | 给题目打0分 | 分数设置为0，计入总分 | 待测试 | 边界值分析 |
| 22 | 分数边界值-满分 | 给10分题目打10分 | 分数设置为10，计入总分 | 待测试 | 边界值分析 |
| 23 | 分数边界值-0.5分 | 给题目打0.5分 | 支持小数分数输入 | 待测试 | 边界值分析 |
| 24 | 分数边界值-9.9分 | 给10分题目打9.9分 | 分数设置成功 | 待测试 | 边界值分析 |
| 25 | 分数精度测试 | 输入多位小数分数 | 按系统精度要求处理 | 待测试 | 精度测试 |

## 评阅工作流测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 26 | 评阅流程-开始 | 从待评阅状态开始评阅 | 状态变为评阅中 | 待测试 | 工作流测试 |
| 27 | 评阅流程-暂停 | 评阅过程中暂停保存 | 状态保持为评阅中 | 待测试 | 工作流测试 |
| 28 | 评阅流程-完成 | 完成所有题目评阅 | 状态变为评阅完成 | 待测试 | 工作流测试 |
| 29 | 评阅流程-发布 | 发布评阅结果给学生 | 学生可以查看成绩 | 待测试 | 工作流测试 |
| 30 | 评阅流程-撤回 | 撤回已发布的成绩 | 成绩撤回，学生不可见 | 待测试 | 工作流测试 |

## 成绩统计分析测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 31 | 总分计算 | 计算答卷总分 | 总分计算正确 | 待测试 | 计算功能测试 |
| 32 | 平均分统计 | 统计班级平均分 | 平均分计算正确 | 待测试 | 统计功能测试 |
| 33 | 最高分最低分 | 统计最高分和最低分 | 统计结果正确 | 待测试 | 统计功能测试 |
| 34 | 分数分布统计 | 统计各分数段人数分布 | 分布统计正确 | 待测试 | 分布统计测试 |
| 35 | 题目得分率 | 统计各题目的得分率 | 得分率计算正确 | 待测试 | 得分率测试 |

## 批量操作测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 36 | 批量设置分数 | 为多个相同答案设置相同分数 | 批量设置成功 | 待测试 | 批量操作测试 |
| 37 | 批量添加评语 | 为多个答卷添加相同评语 | 批量评语添加成功 | 待测试 | 批量操作测试 |
| 38 | 批量发布成绩 | 批量发布多个答卷成绩 | 批量发布成功 | 待测试 | 批量操作测试 |
| 39 | 批量导出成绩 | 批量导出成绩到Excel | 成绩导出成功 | 待测试 | 导出功能测试 |
| 40 | 评阅进度跟踪 | 跟踪批量评阅的进度 | 进度显示准确 | 待测试 | 进度跟踪测试 |

## 测试数据准备
1. **答卷数据**: 准备各种状态的学生答卷
2. **题目数据**: 准备不同类型的题目和标准答案
3. **用户账号**: 准备教师和学生测试账号
4. **评分标准**: 准备各种评分标准和规则
5. **成绩数据**: 准备用于统计分析的成绩数据

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari
- 网络: 稳定的互联网连接
- 账号: 有效的教师测试账号
- 权限: 答卷评阅权限
- 数据: 包含学生答卷的测试数据
- 计算: 支持复杂的分数计算和统计
