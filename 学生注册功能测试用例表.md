# 学生注册功能测试用例表

## 模块信息
- **模块编号**: KSK-ZC-001
- **模块名称**: 注册
- **功能名称**: 学生注册
- **测试方法**: 等价类划分、边界值分析、决策表

## 等价类划分

### 用户名等价类
- **有效等价类**:
  - E1: 未被占用的用户名(1-7个汉字或1-14个字母/数字/下划线)
- **无效等价类**:
  - E2: 已被占用的用户名
  - E3: 空用户名
  - E4: 超长用户名(>7个汉字或>14个字符)
  - E5: 包含非法字符的用户名

### 密码等价类
- **有效等价类**:
  - E6: 符合复杂度要求的密码(6-32个字符，包含两种以上字符类型)
- **无效等价类**:
  - E7: 过短密码(<6个字符)
  - E8: 过长密码(>32个字符)
  - E9: 单一字符类型密码
  - E10: 空密码

### 确认密码等价类
- **有效等价类**:
  - E11: 与密码一致的确认密码
- **无效等价类**:
  - E12: 与密码不一致的确认密码
  - E13: 空确认密码

### 邮箱等价类
- **有效等价类**:
  - E14: 有效的邮箱格式
- **无效等价类**:
  - E15: 无效的邮箱格式
  - E16: 空邮箱
  - E17: 已被注册的邮箱

### 身份选择等价类
- **有效等价类**:
  - E18: 选择"学生|考生"
- **无效等价类**:
  - E19: 未选择身份

### 性别等价类
- **有效等价类**:
  - E20: 选择"男"或"女"
- **无效等价类**:
  - E21: 未选择性别

### 验证码等价类
- **有效等价类**:
  - E22: 正确的验证码
- **无效等价类**:
  - E23: 错误的验证码
  - E24: 空验证码

## 测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 正常学生注册 | 输入有效用户名、符合要求的密码、一致的确认密码、有效邮箱、选择学生身份和性别、正确验证码，点击"注册" | 注册成功，跳转到登录页面或成功页面 | 待测试 | 等价类E1+E6+E11+E14+E18+E20+E22 |
| 2 | 用户名为空 | 用户名留空，其他信息正确填写 | 提示"请输入用户名" | 待测试 | 等价类E3 |
| 3 | 密码为空 | 密码留空，其他信息正确填写 | 提示"请输入密码" | 待测试 | 等价类E10 |
| 4 | 确认密码为空 | 确认密码留空，其他信息正确填写 | 提示"请输入确认密码" | 待测试 | 等价类E13 |
| 5 | 邮箱为空 | 邮箱留空，其他信息正确填写 | 提示"请输入邮箱" | 待测试 | 等价类E16 |
| 6 | 验证码为空 | 验证码留空，其他信息正确填写 | 提示"请输入验证码" | 待测试 | 等价类E24 |
| 7 | 用户名已存在 | 输入已注册的用户名，其他信息正确 | 提示"用户名已存在" | 待测试 | 等价类E2 |
| 8 | 密码不符合复杂度 | 输入纯数字密码，其他信息正确 | 提示"密码必须包含两种以上字符类型" | 待测试 | 等价类E9 |
| 9 | 确认密码不一致 | 密码和确认密码输入不同内容，其他信息正确 | 提示"两次输入的密码不一致" | 待测试 | 等价类E12 |
| 10 | 邮箱格式错误 | 输入无效邮箱格式(如"test@")，其他信息正确 | 提示"邮箱格式不正确" | 待测试 | 等价类E15 |
| 11 | 验证码错误 | 输入错误验证码，其他信息正确 | 提示"验证码错误" | 待测试 | 等价类E23 |
| 12 | 未选择身份 | 不选择身份类型，其他信息正确 | 提示"请选择身份" | 待测试 | 等价类E19 |
| 13 | 未选择性别 | 不选择性别，其他信息正确 | 提示"请选择性别"或允许注册 | 待测试 | 等价类E21 |
| 14 | 用户名边界值-最短汉字 | 输入1个汉字用户名，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 15 | 用户名边界值-最长汉字 | 输入7个汉字用户名，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 16 | 用户名边界值-超长汉字 | 输入8个汉字用户名，其他信息正确 | 提示用户名过长或自动截断 | 待测试 | 边界值分析 |
| 17 | 用户名边界值-最短字符 | 输入1个字母用户名，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 18 | 用户名边界值-最长字符 | 输入14个字母用户名，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 19 | 用户名边界值-超长字符 | 输入15个字母用户名，其他信息正确 | 提示用户名过长或自动截断 | 待测试 | 边界值分析 |
| 20 | 密码边界值-最短 | 输入6个字符的复合密码，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 21 | 密码边界值-过短 | 输入5个字符的密码，其他信息正确 | 提示密码长度不符合要求 | 待测试 | 边界值分析 |
| 22 | 密码边界值-最长 | 输入32个字符的复合密码，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 23 | 密码边界值-过长 | 输入33个字符的密码，其他信息正确 | 提示密码长度不符合要求或自动截断 | 待测试 | 边界值分析 |
| 24 | 特殊字符用户名 | 输入包含空格、特殊符号的用户名，其他信息正确 | 提示用户名格式错误 | 待测试 | 等价类E5 |
| 25 | 邮箱已被注册 | 输入已注册的邮箱，其他信息正确 | 提示"邮箱已被注册" | 待测试 | 等价类E17 |

## 决策表分析

### 条件
| 条件 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 用户名有效 | Y | Y | Y | Y | Y | Y | Y | N |
| 密码有效 | Y | Y | Y | Y | N | N | N | - |
| 确认密码一致 | Y | Y | N | N | - | - | - | - |
| 邮箱有效 | Y | N | - | - | - | - | - | - |
| 身份已选择 | Y | Y | Y | N | Y | Y | N | - |
| 验证码正确 | Y | Y | Y | Y | Y | N | Y | - |

### 动作
| 动作 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 注册成功 | X | | | | | | | |
| 提示邮箱错误 | | X | | | | | | |
| 提示密码不一致 | | | X | | | | | |
| 提示选择身份 | | | | X | | | X | |
| 提示密码无效 | | | | | X | | | |
| 提示验证码错误 | | | | | | X | | |
| 提示用户名无效 | | | | | | | | X |

## 测试数据准备
1. **有效用户名**: 准备不同长度的汉字和字母数字组合
2. **无效用户名**: 准备已存在用户名、超长用户名、特殊字符用户名
3. **密码组合**: 准备各种复杂度的密码
4. **邮箱数据**: 准备有效和无效格式的邮箱地址
5. **边界值数据**: 准备临界长度的输入数据

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari
- 网络: 稳定的互联网连接
- JavaScript: 启用状态
- Cookie: 启用状态
