# 录入试卷功能测试表

## 模块信息
- **模块编号**: KSK-SJ-001
- **模块名称**: 录入试卷
- **功能名称**: 手工录入试卷、在线编辑试卷
- **测试方法**: 等价类划分、边界值分析、决策表、因果图

## 功能描述
支持纯手工录入、在线编辑试卷，简单得就跟录入WORD文档一样。支持单选题、多选题、判断题、填空题、问答|计算|分析等各类文字题和图片题。

## 等价类划分

### 试卷标题等价类
- **有效等价类**:
  - E1: 有效的试卷标题(1-100个字符)
- **无效等价类**:
  - E2: 空试卷标题
  - E3: 超长试卷标题(>100个字符)
  - E4: 包含特殊字符的标题

### 试卷描述等价类
- **有效等价类**:
  - E5: 有效的试卷描述(0-500个字符)
- **无效等价类**:
  - E6: 超长试卷描述(>500个字符)

### 题目类型等价类
- **有效等价类**:
  - E7: 单选题
  - E8: 多选题
  - E9: 判断题
  - E10: 填空题
  - E11: 问答题
  - E12: 计算题
  - E13: 分析题
- **无效等价类**:
  - E14: 未选择题目类型

### 题目内容等价类
- **有效等价类**:
  - E15: 纯文字题目
  - E16: 包含图片的题目
  - E17: 包含表格的题目
- **无效等价类**:
  - E18: 空题目内容
  - E19: 超长题目内容

### 答案选项等价类
- **有效等价类**:
  - E20: 2-10个选项(单选/多选)
  - E21: 正确/错误(判断题)
  - E22: 标准答案(填空/问答)
- **无效等价类**:
  - E23: 选项数量不足
  - E24: 未设置正确答案
  - E25: 答案格式错误

## 测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 创建新试卷 | 输入试卷标题"期中考试"、描述"高一数学期中考试"、选择科目，点击"创建" | 试卷创建成功，进入编辑页面 | 待测试 | 等价类E1+E5 |
| 2 | 试卷标题为空 | 试卷标题留空，其他信息正确，点击创建 | 提示"请输入试卷标题" | 待测试 | 等价类E2 |
| 3 | 试卷标题过长 | 输入101个字符的标题，其他信息正确 | 提示"标题过长"或自动截断 | 待测试 | 等价类E3 |
| 4 | 添加单选题 | 点击"添加单选题"，输入题目和4个选项，设置正确答案 | 单选题添加成功 | 待测试 | 等价类E7+E15+E20 |
| 5 | 添加多选题 | 点击"添加多选题"，输入题目和5个选项，设置多个正确答案 | 多选题添加成功 | 待测试 | 等价类E8+E15+E20 |
| 6 | 添加判断题 | 点击"添加判断题"，输入题目，设置正确/错误答案 | 判断题添加成功 | 待测试 | 等价类E9+E15+E21 |
| 7 | 添加填空题 | 点击"添加填空题"，输入题目，设置标准答案 | 填空题添加成功 | 待测试 | 等价类E10+E15+E22 |
| 8 | 添加问答题 | 点击"添加问答题"，输入题目，设置参考答案 | 问答题添加成功 | 待测试 | 等价类E11+E15+E22 |
| 9 | 题目内容为空 | 添加题目时内容留空，设置选项和答案 | 提示"请输入题目内容" | 待测试 | 等价类E18 |
| 10 | 未设置正确答案 | 添加单选题但不设置正确答案 | 提示"请设置正确答案" | 待测试 | 等价类E24 |
| 11 | 选项数量不足 | 单选题只设置1个选项 | 提示"选项数量不足" | 待测试 | 等价类E23 |
| 12 | 添加图片题目 | 在题目中插入图片，设置选项和答案 | 图片题目添加成功 | 待测试 | 等价类E16 |
| 13 | 添加表格题目 | 在题目中插入表格，设置选项和答案 | 表格题目添加成功 | 待测试 | 等价类E17 |
| 14 | 设置题目分值 | 为题目设置分值(1-100分) | 分值设置成功 | 待测试 | 分值设置测试 |
| 15 | 题目排序 | 调整题目在试卷中的顺序 | 题目顺序调整成功 | 待测试 | 排序功能测试 |
| 16 | 删除题目 | 删除试卷中的某个题目 | 题目删除成功 | 待测试 | 删除功能测试 |
| 17 | 编辑题目 | 修改已添加题目的内容和选项 | 题目修改成功 | 待测试 | 编辑功能测试 |
| 18 | 复制题目 | 复制试卷中的题目 | 题目复制成功 | 待测试 | 复制功能测试 |
| 19 | 预览试卷 | 点击"预览试卷"查看试卷效果 | 试卷预览正常显示 | 待测试 | 预览功能测试 |
| 20 | 保存试卷 | 完成试卷编辑后点击"保存" | 试卷保存成功 | 待测试 | 保存功能测试 |

## 题目类型专项测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 21 | 单选题-2个选项 | 创建只有2个选项的单选题 | 单选题创建成功 | 待测试 | 边界值分析 |
| 22 | 单选题-10个选项 | 创建有10个选项的单选题 | 单选题创建成功 | 待测试 | 边界值分析 |
| 23 | 多选题-全选正确 | 创建所有选项都正确的多选题 | 多选题创建成功 | 待测试 | 特殊情况测试 |
| 24 | 填空题-多个空格 | 创建包含多个填空的题目 | 填空题创建成功，每个空格可单独计分 | 待测试 | 复杂题型测试 |
| 25 | 问答题-长文本答案 | 创建需要长篇回答的问答题 | 问答题创建成功，支持长文本 | 待测试 | 长文本测试 |

## 试卷设置测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 26 | 设置试卷总分 | 设置试卷总分为100分 | 总分设置成功，各题分值自动调整 | 待测试 | 分值管理测试 |
| 27 | 设置考试时间 | 设置考试时长为120分钟 | 时间设置成功 | 待测试 | 时间设置测试 |
| 28 | 设置试卷难度 | 设置试卷难度为"中等" | 难度设置成功 | 待测试 | 难度设置测试 |
| 29 | 设置试卷公开性 | 设置试卷为公开/私有 | 公开性设置成功 | 待测试 | 权限设置测试 |
| 30 | 设置答题说明 | 添加试卷答题说明和注意事项 | 说明添加成功 | 待测试 | 说明设置测试 |

## 决策表分析

### 添加题目决策表
| 条件 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 题目内容有效 | Y | Y | Y | Y | N | N | N | N |
| 选项设置正确 | Y | Y | N | N | Y | Y | N | N |
| 答案设置正确 | Y | N | Y | N | Y | N | Y | N |

| 动作 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 题目添加成功 | X | | | | | | | |
| 提示设置答案 | | X | | X | | X | | X |
| 提示设置选项 | | | X | X | | | X | X |
| 提示输入内容 | | | | | X | X | X | X |

## 富文本编辑测试

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 31 | 文字格式设置 | 设置题目文字的字体、大小、颜色 | 格式设置成功 | 待测试 | 格式化测试 |
| 32 | 插入数学公式 | 在题目中插入数学公式 | 公式插入成功，显示正确 | 待测试 | 公式编辑测试 |
| 33 | 插入特殊符号 | 在题目中插入特殊符号 | 符号插入成功 | 待测试 | 符号插入测试 |
| 34 | 文本对齐设置 | 设置题目文本的对齐方式 | 对齐设置成功 | 待测试 | 对齐测试 |
| 35 | 列表格式设置 | 在题目中使用有序/无序列表 | 列表格式正确显示 | 待测试 | 列表格式测试 |

## 测试数据准备
1. **试卷模板**: 准备不同类型的试卷模板
2. **题目内容**: 准备各种类型的题目内容
3. **图片文件**: 准备不同格式和大小的图片
4. **答案数据**: 准备各种格式的标准答案
5. **分值设置**: 准备不同的分值分配方案

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari (支持富文本编辑)
- 网络: 稳定的互联网连接
- 账号: 有效的教师测试账号
- 权限: 试卷录入权限
- 插件: 支持图片上传和公式编辑
