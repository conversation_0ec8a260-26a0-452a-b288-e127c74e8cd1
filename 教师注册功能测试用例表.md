# 教师注册功能测试用例表

## 模块信息
- **模块编号**: KSK-ZC-002
- **模块名称**: 注册
- **功能名称**: 教师注册
- **测试方法**: 等价类划分、边界值分析、决策表

## 等价类划分

### 用户名等价类
- **有效等价类**:
  - E1: 未被占用的用户名(1-7个汉字或1-14个字母/数字/下划线)
- **无效等价类**:
  - E2: 已被占用的用户名
  - E3: 空用户名
  - E4: 超长用户名(>7个汉字或>14个字符)
  - E5: 包含非法字符的用户名

### 密码等价类
- **有效等价类**:
  - E6: 符合复杂度要求的密码(6-32个字符，包含两种以上字符类型)
- **无效等价类**:
  - E7: 过短密码(<6个字符)
  - E8: 过长密码(>32个字符)
  - E9: 单一字符类型密码
  - E10: 空密码

### 确认密码等价类
- **有效等价类**:
  - E11: 与密码一致的确认密码
- **无效等价类**:
  - E12: 与密码不一致的确认密码
  - E13: 空确认密码

### 邮箱等价类
- **有效等价类**:
  - E14: 有效的邮箱格式
- **无效等价类**:
  - E15: 无效的邮箱格式
  - E16: 空邮箱
  - E17: 已被注册的邮箱

### 身份选择等价类
- **有效等价类**:
  - E18: 选择"老师|考试主管"
- **无效等价类**:
  - E19: 未选择身份

### 性别等价类
- **有效等价类**:
  - E20: 选择"男"或"女"
- **无效等价类**:
  - E21: 未选择性别

### 验证码等价类
- **有效等价类**:
  - E22: 正确的验证码
- **无效等价类**:
  - E23: 错误的验证码
  - E24: 空验证码

## 测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 正常教师注册 | 输入有效用户名、符合要求的密码、一致的确认密码、有效邮箱、选择教师身份和性别、正确验证码，点击"注册" | 注册成功，跳转到登录页面或成功页面 | 待测试 | 等价类E1+E6+E11+E14+E18+E20+E22 |
| 2 | 用户名为空 | 用户名留空，其他信息正确填写 | 提示"请输入用户名" | 待测试 | 等价类E3 |
| 3 | 密码为空 | 密码留空，其他信息正确填写 | 提示"请输入密码" | 待测试 | 等价类E10 |
| 4 | 确认密码为空 | 确认密码留空，其他信息正确填写 | 提示"请输入确认密码" | 待测试 | 等价类E13 |
| 5 | 邮箱为空 | 邮箱留空，其他信息正确填写 | 提示"请输入邮箱" | 待测试 | 等价类E16 |
| 6 | 验证码为空 | 验证码留空，其他信息正确填写 | 提示"请输入验证码" | 待测试 | 等价类E24 |
| 7 | 用户名已存在 | 输入已注册的用户名，其他信息正确 | 提示"用户名已存在" | 待测试 | 等价类E2 |
| 8 | 密码复杂度不够 | 输入纯数字密码"123456"，其他信息正确 | 提示"密码必须包含两种以上字符类型" | 待测试 | 等价类E9 |
| 9 | 确认密码不一致 | 密码输入"Test123"，确认密码输入"Test456"，其他信息正确 | 提示"两次输入的密码不一致" | 待测试 | 等价类E12 |
| 10 | 邮箱格式错误 | 输入"teacher@"，其他信息正确 | 提示"邮箱格式不正确" | 待测试 | 等价类E15 |
| 11 | 验证码错误 | 输入错误验证码，其他信息正确 | 提示"验证码错误" | 待测试 | 等价类E23 |
| 12 | 未选择教师身份 | 选择"学生|考生"身份，其他信息正确 | 注册为学生账号或提示身份选择错误 | 待测试 | 身份选择测试 |
| 13 | 未选择性别 | 不选择性别，其他信息正确 | 提示"请选择性别"或允许注册 | 待测试 | 等价类E21 |
| 14 | 教师用户名边界值-最短汉字 | 输入1个汉字"张"，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 15 | 教师用户名边界值-最长汉字 | 输入7个汉字"张三李四王五赵六"，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 16 | 教师用户名边界值-超长汉字 | 输入8个汉字"张三李四王五赵六孙"，其他信息正确 | 提示用户名过长或自动截断 | 待测试 | 边界值分析 |
| 17 | 教师用户名边界值-最短字符 | 输入"T"，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 18 | 教师用户名边界值-最长字符 | 输入"Teacher_123456"(14个字符)，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 19 | 教师用户名边界值-超长字符 | 输入"Teacher_1234567"(15个字符)，其他信息正确 | 提示用户名过长或自动截断 | 待测试 | 边界值分析 |
| 20 | 教师密码边界值-最短 | 输入"Aa123!"(6个字符)，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 21 | 教师密码边界值-过短 | 输入"Aa12"(5个字符)，其他信息正确 | 提示密码长度不符合要求 | 待测试 | 边界值分析 |
| 22 | 教师密码边界值-最长 | 输入32个字符的复合密码，其他信息正确 | 注册成功 | 待测试 | 边界值分析 |
| 23 | 教师密码边界值-过长 | 输入33个字符的密码，其他信息正确 | 提示密码长度不符合要求或自动截断 | 待测试 | 边界值分析 |
| 24 | 教师专用邮箱格式 | 输入教育邮箱"<EMAIL>"，其他信息正确 | 注册成功 | 待测试 | 特殊格式测试 |
| 25 | 特殊字符用户名 | 输入"Teacher@123"，其他信息正确 | 提示用户名格式错误 | 待测试 | 等价类E5 |
| 26 | 邮箱已被注册 | 输入已注册的邮箱，其他信息正确 | 提示"邮箱已被注册" | 待测试 | 等价类E17 |
| 27 | 教师身份权限验证 | 注册成功后登录，验证是否具有教师功能权限 | 具有创建班级、录入试卷等教师功能 | 待测试 | 权限测试 |

## 决策表分析

### 条件
| 条件 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 用户名有效 | Y | Y | Y | Y | Y | Y | Y | N |
| 密码有效 | Y | Y | Y | Y | N | N | N | - |
| 确认密码一致 | Y | Y | N | N | - | - | - | - |
| 邮箱有效 | Y | N | - | - | - | - | - | - |
| 选择教师身份 | Y | Y | Y | N | Y | Y | N | - |
| 验证码正确 | Y | Y | Y | Y | Y | N | Y | - |

### 动作
| 动作 | 规则1 | 规则2 | 规则3 | 规则4 | 规则5 | 规则6 | 规则7 | 规则8 |
|------|-------|-------|-------|-------|-------|-------|-------|-------|
| 注册成功(教师权限) | X | | | | | | | |
| 提示邮箱错误 | | X | | | | | | |
| 提示密码不一致 | | | X | | | | | |
| 提示选择教师身份 | | | | X | | | X | |
| 提示密码无效 | | | | | X | | | |
| 提示验证码错误 | | | | | | X | | |
| 提示用户名无效 | | | | | | | | X |

## 教师注册特殊测试场景

| 编号 | 测试场景 | 测试步骤 | 预期结果 | 测试方法 |
|------|---------|---------|---------|---------|
| 28 | 教师权限验证 | 1.完成教师注册 2.登录系统 3.检查功能菜单 | 显示教师专用功能(班级管理、试卷录入等) | 功能测试 |
| 29 | 教师与学生账号区分 | 1.注册教师账号 2.注册学生账号 3.分别登录对比 | 两种账号功能权限不同 | 对比测试 |
| 30 | 教师账号升级 | 学生账号是否可以升级为教师账号 | 根据系统设计确定是否支持 | 业务逻辑测试 |

## 测试数据准备
1. **教师用户名**: 准备符合教师身份的用户名
2. **教师邮箱**: 准备教育机构邮箱和普通邮箱
3. **教师密码**: 准备符合安全要求的密码组合
4. **边界值数据**: 准备各种长度的输入数据
5. **权限验证数据**: 准备用于验证教师权限的测试数据

## 测试环境要求
- 浏览器: Chrome, Firefox, Safari, Edge
- 网络: 稳定的互联网连接
- JavaScript: 启用状态
- Cookie: 启用状态
- 分辨率: 1920x1080及以上
