# 软件测试期末大作业 - 考试酷网站黑盒测试

## 项目概述
- **被测系统**: 考试酷 (https://www.examcoo.com/)
- **测试类型**: 黑盒测试
- **测试方法**: 等价类划分、边界值分析、因果图、决策表
- **测试目标**: 验证考试酷网站核心功能的正确性和可靠性

## 一、测试计划表

| 模块编号 | 模块名称 | 功能名称 | 需求优先级 | 测试状态 |
|---------|---------|---------|-----------|---------|
| KSK-DL-001 | 登录 | 用户登录 | 高 | 已设计测试用例 |
| KSK-ZC-001 | 注册 | 学生注册 | 高 | 已设计测试用例 |
| KSK-ZC-002 | 注册 | 教师注册 | 高 | 已设计测试用例 |
| KSK-LS-001 | 教师-我的班级 | 创建班级 | 高 | 已设计测试用例 |
| KSK-LS-002 | 教师-我的班级 | 申请加入班级 | 高 | 已设计测试用例 |
| KSK-XX-001 | 教师-我的消息 | 发件箱 | 高 | 已设计测试用例 |
| KSK-XX-002 | 教师-我的消息 | 收件箱 | 高 | 已设计测试用例 |
| KSK-GL-001 | 管理科目 | 设置感兴趣科目 | 中 | 已设计测试用例 |
| KSK-SJ-001 | 录入试卷 | 手工录入试卷 | 中 | 已设计测试用例 |
| KSK-BJ-001 | 创建班级 | 教师创建虚拟班级 | 高 | 已设计测试用例 |
| KSK-BJ-002 | 申请加入班级 | 学生申请加入班级 | 高 | 已设计测试用例 |
| KSK-DB-001 | 查看待办事宜 | 待办事宜中心 | 中 | 已设计测试用例 |
| KSK-KS-001 | 参加班级考试 | 学生参加在线考试 | 高 | 已设计测试用例 |
| KSK-GJ-001 | 工具箱 | 试卷转让等工具 | 低 | 已设计测试用例 |
| KSK-TK-001 | 公共题库中心 | 题库浏览搜索使用 | 中 | 已设计测试用例 |
| KSK-PY-001 | 答卷评阅 | 手工阅卷成绩管理 | 高 | 已设计测试用例 |

## 二、测试用例统计

| 功能模块 | 测试用例数量 | 覆盖的测试方法 |
|---------|-------------|---------------|
| 登录功能 | 20个 | 等价类、边界值、因果图、安全测试 |
| 学生注册 | 25个 | 等价类、边界值、决策表 |
| 教师注册 | 30个 | 等价类、边界值、决策表、权限测试 |
| 班级管理(原) | 32个 | 等价类、边界值、因果图、决策表 |
| 消息功能 | 35个 | 等价类、边界值、因果图、决策表 |
| 管理科目 | 30个 | 等价类、边界值、决策表 |
| 录入试卷 | 35个 | 等价类、边界值、决策表、富文本测试 |
| 创建班级 | 35个 | 等价类、边界值、决策表、因果图 |
| 申请加入班级 | 35个 | 等价类、边界值、决策表、状态转换 |
| 查看待办事宜 | 40个 | 等价类、边界值、状态转换、功能测试 |
| 参加班级考试 | 40个 | 等价类、边界值、状态转换、时间测试 |
| 工具箱 | 40个 | 等价类、边界值、功能测试、集成测试 |
| 公共题库中心 | 40个 | 等价类、边界值、搜索测试、权限测试 |
| 答卷评阅 | 40个 | 等价类、边界值、工作流测试、权限测试 |
| **总计** | **477个** | **全面覆盖各种黑盒测试方法** |

## 三、测试方法应用说明

### 3.1 等价类划分
- **应用场景**: 所有输入字段的有效性验证
- **划分原则**:
  - 有效等价类：符合系统要求的输入数据
  - 无效等价类：不符合系统要求的输入数据
- **典型应用**: 用户名、密码、邮箱格式验证

### 3.2 边界值分析
- **应用场景**: 输入长度限制、数值范围验证
- **测试重点**: 最小值、最大值、最小值-1、最大值+1
- **典型应用**: 用户名长度(1-14字符)、密码长度(6-32字符)

### 3.3 因果图
- **应用场景**: 多个输入条件组合的逻辑关系
- **分析方法**: 输入条件 → 中间节点 → 输出结果
- **典型应用**: 登录成功条件、消息发送条件

### 3.4 决策表
- **应用场景**: 复杂业务规则的组合测试
- **构建方法**: 列出所有条件组合及对应动作
- **典型应用**: 注册流程、权限验证

## 四、测试文档清单

### 4.1 基础功能测试表
1. `登录功能测试表.md` - 20个测试用例
2. `学生注册功能测试表.md` - 25个测试用例
3. `教师注册功能测试表.md` - 30个测试用例

### 4.2 教师功能测试表
4. `管理科目功能测试表.md` - 30个测试用例
5. `录入试卷功能测试表.md` - 35个测试用例
6. `创建班级功能测试表.md` - 35个测试用例
7. `申请加入班级功能测试表.md` - 35个测试用例
8. `教师-我的班级功能测试表.md` - 32个测试用例
9. `教师-我的消息功能测试表.md` - 35个测试用例
10. `查看待办事宜功能测试表.md` - 40个测试用例
11. `答卷评阅功能测试表.md` - 40个测试用例

### 4.3 学生功能测试表
12. `参加班级考试功能测试表.md` - 40个测试用例

### 4.4 公共功能测试表
13. `工具箱功能测试表.md` - 40个测试用例
14. `公共题库中心功能测试表.md` - 40个测试用例

### 4.5 项目文档
15. `测试计划表.md` - 测试计划和优先级
16. `README.md` - 项目说明和使用指南
17. `软件测试期末大作业-完整版.md` - 完整测试报告

## 五、测试环境配置

### 5.1 硬件环境
- **CPU**: Intel i5 或以上
- **内存**: 8GB 或以上
- **硬盘**: 100GB 可用空间
- **网络**: 稳定的宽带连接

### 5.2 软件环境
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **浏览器**:
  - Chrome 90+ (主要测试浏览器)
  - Firefox 88+
  - Safari 14+
  - Edge 90+
- **分辨率**: 1920x1080 及以上

### 5.3 测试数据准备
- **有效用户账号**: 学生账号10个，教师账号10个
- **测试班级**: 公开班级5个，私有班级3个
- **测试试卷**: 各种类型试卷20套
- **测试题目**: 各种题型题目500道
- **边界值数据**: 各种临界长度的输入数据
- **异常数据**: 各种异常和错误数据

## 六、质量标准与验收标准

### 6.1 功能正确性标准
- **核心功能**: 登录、注册、考试功能100%正常工作
- **重要功能**: 班级管理、试卷管理功能95%以上正常工作
- **辅助功能**: 工具箱、题库功能90%以上正常工作

### 6.2 用户体验标准
- **界面友好性**: 用户操作流程清晰直观
- **响应时间**: 页面加载时间≤3秒
- **错误处理**: 错误提示信息准确友好

### 6.3 安全性标准
- **输入验证**: 所有输入字段都有有效性验证
- **权限控制**: 用户权限控制严格有效
- **数据安全**: 用户数据传输和存储安全

### 6.4 验收标准
- **高优先级功能**: 0个严重缺陷，≤2个一般缺陷
- **中优先级功能**: ≤1个严重缺陷，≤5个一般缺陷
- **低优先级功能**: ≤2个严重缺陷，≤10个一般缺陷

## 七、测试执行指南

### 7.1 测试执行顺序
1. **第一阶段**: 基础功能测试(登录、注册) - 建议2天
2. **第二阶段**: 核心功能测试(班级管理、考试功能) - 建议3天
3. **第三阶段**: 辅助功能测试(工具箱、题库) - 建议2天
4. **第四阶段**: 集成测试和回归测试 - 建议1天

### 7.2 测试注意事项
1. **数据隔离**: 使用专门的测试账号和测试数据
2. **环境准备**: 确保测试环境稳定可靠
3. **结果记录**: 及时记录测试结果和发现的问题
4. **问题跟踪**: 建立缺陷跟踪和管理机制

## 八、项目总结

### 8.1 项目成果
- **测试用例总数**: 477个
- **覆盖功能模块**: 14个主要功能模块
- **测试方法应用**: 等价类、边界值、因果图、决策表等
- **文档完整性**: 包含详细的测试计划、用例和指南

### 8.2 项目特色
1. **方法全面**: 运用了多种黑盒测试方法
2. **覆盖完整**: 按照优先级覆盖了网站的核心功能
3. **设计规范**: 每个测试用例都包含完整的测试信息
4. **实用性强**: 所有测试用例都可以直接执行

### 8.3 后续建议
1. **持续更新**: 根据系统更新及时维护测试用例
2. **自动化**: 考虑将部分测试用例自动化执行
3. **性能测试**: 补充系统性能和压力测试
4. **用户体验**: 增加用户体验和可用性测试

---

**文档版本**: V2.0
**创建日期**: 2024年12月19日
**最后更新**: 2024年12月19日
**创建人**: 测试团队
**审核状态**: 已完成

## 附录：测试用例执行记录模板

### 缺陷记录格式
```
缺陷编号: BUG-001
缺陷标题: [功能模块] 具体问题描述
发现时间: 2024-XX-XX
测试环境: 浏览器版本 + 操作系统
重现步骤:
1. 详细步骤1
2. 详细步骤2
3. 详细步骤3
预期结果: 期望的正确结果
实际结果: 实际观察到的结果
严重程度: 严重/中等/轻微
优先级: 高/中/低
状态: 新建/已确认/已修复/已验证/已关闭
```

### 测试执行记录表
| 测试用例编号 | 执行日期 | 执行人 | 执行结果 | 缺陷编号 | 备注 |
|-------------|---------|-------|---------|---------|------|
| TC-001 | 2024-XX-XX | 测试员 | 通过/失败 | BUG-001 | 备注信息 |