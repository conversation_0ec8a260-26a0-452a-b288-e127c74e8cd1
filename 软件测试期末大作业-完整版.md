# 软件测试期末大作业

## 项目概述
- **被测系统**: 考试酷 (https://www.examcoo.com/)
- **测试类型**: 黑盒测试
- **测试方法**: 等价类划分、边界值分析、因果图、决策表
- **测试目标**: 验证考试酷网站核心功能的正确性和可靠性

## 一、测试计划表

| 模块编号 | 模块名称 | 功能名称 | 需求优先级 | 测试状态 |
|---------|---------|---------|-----------|---------|
| KSK-DL-001 | 登录 | 用户登录 | 高 | 已设计测试用例 |
| KSK-ZC-001 | 注册 | 学生注册 | 高 | 已设计测试用例 |
| KSK-ZC-002 | 注册 | 教师注册 | 高 | 已设计测试用例 |
| KSK-LS-001 | 教师-我的班级 | 创建班级 | 高 | 已设计测试用例 |
| KSK-LS-002 | 教师-我的班级 | 申请加入班级 | 高 | 已设计测试用例 |
| KSK-XX-001 | 教师-我的消息 | 发件箱 | 高 | 已设计测试用例 |
| KSK-XX-002 | 教师-我的消息 | 收件箱 | 高 | 已设计测试用例 |

## 二、测试用例统计

| 功能模块 | 测试用例数量 | 覆盖的测试方法 |
|---------|-------------|---------------|
| 登录功能 | 20个 | 等价类、边界值、因果图、安全测试 |
| 学生注册 | 25个 | 等价类、边界值、决策表 |
| 教师注册 | 30个 | 等价类、边界值、决策表、权限测试 |
| 班级管理 | 32个 | 等价类、边界值、因果图、决策表 |
| 消息功能 | 35个 | 等价类、边界值、因果图、决策表 |
| **总计** | **142个** | **全面覆盖各种黑盒测试方法** |

## 三、测试方法应用说明

### 3.1 等价类划分
- **应用场景**: 所有输入字段的有效性验证
- **划分原则**: 
  - 有效等价类：符合系统要求的输入数据
  - 无效等价类：不符合系统要求的输入数据
- **典型应用**: 用户名、密码、邮箱格式验证

### 3.2 边界值分析
- **应用场景**: 输入长度限制、数值范围验证
- **测试重点**: 最小值、最大值、最小值-1、最大值+1
- **典型应用**: 用户名长度(1-14字符)、密码长度(6-32字符)

### 3.3 因果图
- **应用场景**: 多个输入条件组合的逻辑关系
- **分析方法**: 输入条件 → 中间节点 → 输出结果
- **典型应用**: 登录成功条件、消息发送条件

### 3.4 决策表
- **应用场景**: 复杂业务规则的组合测试
- **构建方法**: 列出所有条件组合及对应动作
- **典型应用**: 注册流程、权限验证

## 四、测试环境配置

### 4.1 硬件环境
- **CPU**: Intel i5 或以上
- **内存**: 8GB 或以上
- **硬盘**: 100GB 可用空间
- **网络**: 稳定的宽带连接

### 4.2 软件环境
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **浏览器**: 
  - Chrome 90+ (主要测试浏览器)
  - Firefox 88+
  - Safari 14+
  - Edge 90+
- **分辨率**: 1920x1080 及以上

### 4.3 测试数据准备
- **有效用户账号**: 学生账号5个，教师账号5个
- **测试班级**: 公开班级3个，私有班级2个
- **测试消息**: 各种长度和格式的消息内容
- **边界值数据**: 各种临界长度的输入数据

## 五、风险评估与缓解措施

### 5.1 测试风险
1. **网络不稳定**: 可能影响在线测试的执行
2. **数据污染**: 测试数据可能影响生产环境
3. **权限限制**: 某些功能可能需要特殊权限
4. **时间限制**: 测试时间有限，无法覆盖所有功能

### 5.2 缓解措施
1. **网络备份**: 准备多个网络环境进行测试
2. **数据隔离**: 使用专门的测试账号和测试数据
3. **权限申请**: 提前申请必要的测试权限
4. **优先级排序**: 按照功能重要性安排测试顺序

## 六、测试执行计划

### 6.1 测试阶段划分
- **第一阶段**: 基础功能测试(登录、注册) - 2天
- **第二阶段**: 核心功能测试(班级管理) - 2天  
- **第三阶段**: 辅助功能测试(消息功能) - 1天
- **第四阶段**: 集成测试和回归测试 - 1天

### 6.2 测试人员分工
- **功能测试**: 负责执行所有功能测试用例
- **兼容性测试**: 负责不同浏览器的兼容性验证
- **性能测试**: 负责系统响应时间和并发测试
- **安全测试**: 负责输入验证和权限控制测试

## 七、测试结果记录模板

### 7.1 缺陷记录格式
```
缺陷编号: BUG-001
缺陷标题: 登录密码长度验证失效
发现时间: 2024-XX-XX
测试环境: Chrome 浏览器
重现步骤: 
1. 打开登录页面
2. 输入5个字符的密码
3. 点击登录按钮
预期结果: 提示密码长度不符合要求
实际结果: 系统接受了短密码
严重程度: 中等
优先级: 高
状态: 新建
```

### 7.2 测试报告结构
1. **测试概述**
2. **测试执行情况**
3. **缺陷统计分析**
4. **功能覆盖率**
5. **测试结论**
6. **改进建议**

## 八、质量标准

### 8.1 通过标准
- **功能正确性**: 核心功能100%正常工作
- **界面友好性**: 用户操作流程清晰
- **数据完整性**: 数据保存和传输无丢失
- **安全性**: 输入验证和权限控制有效

### 8.2 验收标准
- **高优先级功能**: 0个严重缺陷，≤2个一般缺陷
- **中优先级功能**: ≤1个严重缺陷，≤5个一般缺陷
- **系统稳定性**: 连续运行2小时无崩溃
- **响应时间**: 页面加载时间≤3秒

## 九、附录

### 9.1 测试用例文档清单
1. `登录功能测试用例表.md` - 20个测试用例
2. `学生注册功能测试用例表.md` - 25个测试用例
3. `教师注册功能测试用例表.md` - 30个测试用例
4. `教师-我的班级功能测试用例表.md` - 32个测试用例
5. `教师-我的消息功能测试用例表.md` - 35个测试用例

### 9.2 参考资料
- 考试酷官方网站: https://www.examcoo.com/
- 软件测试理论与实践教材
- 黑盒测试方法指南
- Web应用测试最佳实践

---

**文档版本**: V1.0  
**创建日期**: 2024年12月19日  
**创建人**: 测试团队  
**审核状态**: 待审核
