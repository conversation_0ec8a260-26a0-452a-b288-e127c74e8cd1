# 登录功能测试用例表

## 模块信息
- **模块编号**: KSK-DL-001
- **模块名称**: 登录
- **功能名称**: 用户登录
- **测试方法**: 等价类划分、边界值分析、因果图

## 等价类划分

### 用户名等价类
- **有效等价类**:
  - E1: 已注册的有效用户名(长度1-14个字符)
- **无效等价类**:
  - E2: 未注册的用户名
  - E3: 空用户名
  - E4: 超长用户名(>14个字符)
  - E5: 包含非法字符的用户名

### 密码等价类
- **有效等价类**:
  - E6: 正确的密码(6-32个字符，包含两种以上字符类型)
- **无效等价类**:
  - E7: 错误的密码
  - E8: 空密码
  - E9: 过短密码(<6个字符)
  - E10: 过长密码(>32个字符)
  - E11: 单一字符类型密码

### 验证码等价类
- **有效等价类**:
  - E12: 正确的验证码
- **无效等价类**:
  - E13: 错误的验证码
  - E14: 空验证码
  - E15: 过期的验证码

## 测试用例表

| 编号 | 测试用例 | 测评记录 | 预期结果 | 实际结果 | 测试方法 |
|------|---------|---------|---------|---------|---------|
| 1 | 正常登录 | 输入有效用户名、正确密码、正确验证码，点击"登录" | 登录成功，跳转到用户主页 | 待测试 | 等价类E1+E6+E12 |
| 2 | 用户名为空 | 用户名留空，输入正确密码、正确验证码，点击"登录" | 提示"请输入用户名" | 待测试 | 等价类E3 |
| 3 | 密码为空 | 输入有效用户名，密码留空，输入正确验证码，点击"登录" | 提示"请输入密码" | 待测试 | 等价类E8 |
| 4 | 验证码为空 | 输入有效用户名、正确密码，验证码留空，点击"登录" | 提示"请输入验证码" | 待测试 | 等价类E14 |
| 5 | 用户名不存在 | 输入未注册用户名、任意密码、正确验证码，点击"登录" | 提示"用户名或密码错误" | 待测试 | 等价类E2 |
| 6 | 密码错误 | 输入有效用户名、错误密码、正确验证码，点击"登录" | 提示"用户名或密码错误" | 待测试 | 等价类E7 |
| 7 | 验证码错误 | 输入有效用户名、正确密码、错误验证码，点击"登录" | 提示"验证码错误" | 待测试 | 等价类E13 |
| 8 | 用户名边界值测试-最短 | 输入1个字符的有效用户名、正确密码、正确验证码 | 登录成功或提示用户名格式错误 | 待测试 | 边界值分析 |
| 9 | 用户名边界值测试-最长 | 输入14个字符的有效用户名、正确密码、正确验证码 | 登录成功 | 待测试 | 边界值分析 |
| 10 | 用户名边界值测试-超长 | 输入15个字符的用户名、正确密码、正确验证码 | 提示用户名格式错误或自动截断 | 待测试 | 边界值分析 |
| 11 | 密码边界值测试-最短 | 输入有效用户名、6个字符的正确密码、正确验证码 | 登录成功 | 待测试 | 边界值分析 |
| 12 | 密码边界值测试-过短 | 输入有效用户名、5个字符的密码、正确验证码 | 提示密码长度不符合要求 | 待测试 | 边界值分析 |
| 13 | 密码边界值测试-最长 | 输入有效用户名、32个字符的正确密码、正确验证码 | 登录成功 | 待测试 | 边界值分析 |
| 14 | 密码边界值测试-过长 | 输入有效用户名、33个字符的密码、正确验证码 | 提示密码长度不符合要求或自动截断 | 待测试 | 边界值分析 |
| 15 | 特殊字符用户名 | 输入包含特殊字符的用户名、正确密码、正确验证码 | 提示用户名格式错误或登录失败 | 待测试 | 等价类E5 |
| 16 | 单一字符类型密码 | 输入有效用户名、纯数字密码、正确验证码 | 提示密码复杂度不够 | 待测试 | 等价类E11 |
| 17 | SQL注入测试 | 用户名输入"admin' OR '1'='1"，任意密码、正确验证码 | 登录失败，系统安全 | 待测试 | 安全测试 |
| 18 | 记住登录状态 | 勾选"一个月内自动登录"，正常登录后关闭浏览器重新打开 | 自动登录状态保持 | 待测试 | 功能测试 |
| 19 | 验证码刷新 | 点击验证码图片刷新验证码 | 验证码图片更新 | 待测试 | 功能测试 |
| 20 | 多次登录失败 | 连续5次输入错误密码 | 账户被临时锁定或要求更复杂验证 | 待测试 | 安全测试 |

## 因果图分析

### 输入条件
- C1: 用户名有效
- C2: 密码正确  
- C3: 验证码正确

### 输出结果
- E1: 登录成功
- E2: 提示用户名错误
- E3: 提示密码错误
- E4: 提示验证码错误

### 逻辑关系
- 登录成功: C1 AND C2 AND C3 → E1
- 用户名错误: NOT C1 → E2
- 密码错误: C1 AND NOT C2 → E3  
- 验证码错误: C1 AND C2 AND NOT C3 → E4

## 测试数据准备
1. **有效测试账号**: 准备已注册的学生和教师账号各2个
2. **无效用户名**: 准备未注册的用户名5个
3. **边界值数据**: 准备不同长度的用户名和密码
4. **特殊字符**: 准备包含各种特殊字符的测试数据
5. **安全测试数据**: 准备SQL注入、XSS等攻击测试数据

## 测试环境要求
- 浏览器: Chrome 最新版本
- 网络: 稳定的互联网连接
- 分辨率: 1920x1080
- JavaScript: 启用状态
