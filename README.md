# 软件测试期末大作业 - 考试酷网站黑盒测试

## 项目简介

本项目是针对考试酷网站(https://www.examcoo.com/)进行的黑盒测试设计，运用等价类划分、边界值分析、因果图、决策表等测试方法，对网站的核心功能进行全面的测试用例设计。

## 被测系统

- **网站名称**: 考试酷 (examcoo)
- **网站地址**: https://www.examcoo.com/
- **系统类型**: 在线考试与电子作业系统
- **主要功能**: 用户注册登录、班级管理、试卷管理、在线考试、消息通信等

## 项目结构

```
软件测试作业/
├── README.md                           # 项目说明文档
├── 软件测试期末大作业-完整版.md          # 完整测试报告
├── 测试计划表.md                       # 测试计划和优先级
├── 登录功能测试用例表.md               # 登录功能测试用例(20个)
├── 学生注册功能测试用例表.md           # 学生注册测试用例(25个)
├── 教师注册功能测试用例表.md           # 教师注册测试用例(30个)
├── 教师-我的班级功能测试用例表.md      # 班级管理测试用例(32个)
├── 教师-我的消息功能测试用例表.md      # 消息功能测试用例(35个)
├── 管理科目功能测试表.md               # 管理科目测试用例(30个)
├── 录入试卷功能测试表.md               # 录入试卷测试用例(35个)
├── 创建班级功能测试表.md               # 创建班级测试用例(35个)
├── 申请加入班级功能测试表.md           # 申请加入班级测试用例(35个)
├── 查看待办事宜功能测试表.md           # 查看待办事宜测试用例(40个)
├── 参加班级考试功能测试表.md           # 参加班级考试测试用例(40个)
├── 工具箱功能测试表.md                 # 工具箱测试用例(40个)
├── 公共题库中心功能测试表.md           # 公共题库中心测试用例(40个)
└── 答卷评阅功能测试表.md               # 答卷评阅测试用例(40个)
```

## 测试覆盖范围

### 高优先级功能模块
1. **用户登录** (KSK-DL-001)
   - 用户名密码验证
   - 验证码验证
   - 登录状态保持
   - 安全性测试

2. **学生注册** (KSK-ZC-001)
   - 用户名格式验证
   - 密码复杂度验证
   - 邮箱格式验证
   - 身份选择验证

3. **教师注册** (KSK-ZC-002)
   - 教师身份验证
   - 权限分配验证
   - 注册流程完整性

4. **班级管理** (KSK-LS-001/002)
   - 创建班级功能
   - 申请加入班级
   - 班级成员管理
   - 权限控制

5. **消息功能** (KSK-XX-001/002)
   - 发送消息
   - 接收消息
   - 消息管理
   - 通知功能

## 测试方法应用

### 1. 等价类划分
- **有效等价类**: 符合系统要求的输入数据
- **无效等价类**: 不符合系统要求的输入数据
- **应用场景**: 所有输入字段的格式验证

### 2. 边界值分析
- **测试点**: 最小值、最大值、最小值-1、最大值+1
- **应用场景**: 字符长度限制、数值范围验证
- **典型案例**: 用户名1-14字符、密码6-32字符

### 3. 因果图
- **分析方法**: 输入条件 → 逻辑关系 → 输出结果
- **应用场景**: 多条件组合的业务逻辑
- **典型案例**: 登录成功条件、消息发送条件

### 4. 决策表
- **构建方法**: 条件组合 × 动作结果
- **应用场景**: 复杂业务规则验证
- **典型案例**: 注册流程、权限验证

## 测试用例统计

| 功能模块 | 测试用例数 | 主要测试方法 | 覆盖场景 |
|---------|-----------|-------------|---------|
| 登录功能 | 20个 | 等价类、边界值、安全测试 | 正常登录、异常处理、安全防护 |
| 学生注册 | 25个 | 等价类、边界值、决策表 | 注册流程、数据验证、错误处理 |
| 教师注册 | 30个 | 等价类、边界值、权限测试 | 教师权限、身份验证、功能区分 |
| 班级管理(原) | 32个 | 因果图、决策表、权限测试 | 创建管理、成员操作、权限控制 |
| 消息功能 | 35个 | 等价类、边界值、功能测试 | 发送接收、消息管理、通知机制 |
| 管理科目 | 30个 | 等价类、边界值、决策表 | 科目设置、分类管理、权限控制 |
| 录入试卷 | 35个 | 等价类、边界值、富文本测试 | 试卷创建、题目录入、格式处理 |
| 创建班级 | 35个 | 等价类、边界值、因果图 | 班级创建、权限验证、状态管理 |
| 申请加入班级 | 35个 | 等价类、边界值、状态转换 | 申请流程、审核机制、状态跟踪 |
| 查看待办事宜 | 40个 | 状态转换、功能测试 | 事务管理、提醒机制、处理流程 |
| 参加班级考试 | 40个 | 状态转换、时间测试 | 考试流程、时间控制、防作弊 |
| 工具箱 | 40个 | 功能测试、集成测试 | 工具使用、文件处理、权限控制 |
| 公共题库中心 | 40个 | 搜索测试、权限测试 | 题库浏览、搜索功能、权限管理 |
| 答卷评阅 | 40个 | 工作流测试、权限测试 | 评阅流程、成绩管理、统计分析 |
| **总计** | **477个** | **全方位黑盒测试** | **核心功能全覆盖** |

## 测试环境要求

### 浏览器支持
- Chrome 90+ (主要测试环境)
- Firefox 88+
- Safari 14+
- Edge 90+

### 系统要求
- 操作系统: Windows 10+, macOS 10.15+, Linux
- 内存: 8GB以上
- 网络: 稳定的互联网连接
- 分辨率: 1920x1080以上

## 测试数据准备

### 账号数据
- 学生测试账号: 5个
- 教师测试账号: 5个
- 管理员测试账号: 2个

### 测试内容
- 有效/无效用户名: 各20个
- 有效/无效密码: 各15个
- 有效/无效邮箱: 各10个
- 边界值测试数据: 50组
- 特殊字符测试数据: 30组

## 质量标准

### 功能正确性
- 核心功能100%正常工作
- 错误处理机制完善
- 用户体验流畅

### 安全性要求
- 输入验证有效
- 权限控制严格
- 数据传输安全

### 性能要求
- 页面响应时间 ≤ 3秒
- 并发用户支持 ≥ 100人
- 系统稳定运行 ≥ 24小时

## 使用说明

1. **查看测试计划**: 阅读`测试计划表.md`了解测试范围和优先级
2. **执行测试用例**: 按照各功能模块的测试用例表执行测试
3. **记录测试结果**: 在测试用例表的"实际结果"列记录测试结果
4. **缺陷跟踪**: 发现问题时按照缺陷记录格式进行记录
5. **生成报告**: 根据测试结果生成最终测试报告

## 注意事项

1. **测试环境隔离**: 使用专门的测试账号，避免影响生产数据
2. **数据备份**: 测试前备份重要数据
3. **权限申请**: 确保具有必要的测试权限
4. **时间安排**: 按照测试计划合理安排测试时间
5. **结果记录**: 及时记录测试结果和发现的问题

## 联系信息

- **项目负责人**: [姓名]
- **测试团队**: [团队成员]
- **完成时间**: 2024年12月19日
- **文档版本**: V1.0

---

**说明**: 本项目严格按照软件测试理论进行设计，运用多种黑盒测试方法，确保测试用例的全面性和有效性。所有测试用例都经过仔细设计和审核，可直接用于实际测试执行。
